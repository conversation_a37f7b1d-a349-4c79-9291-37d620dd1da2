import*as n from'./dom.js';function s(l){let e=l.parentElement,t=null;for(;e&&!n.isHTMLFieldSetElement(e);)n.isHTMLLegendElement(e)&&(t=e),e=e.parentElement;let i=(e==null?void 0:e.get<PERSON>ttribute("disabled"))==="";return i&&r(t)?!1:i}function r(l){if(!l)return!1;let e=l.previousElementSibling;for(;e!==null;){if(n.isHTMLLegendElement(e))return!1;e=e.previousElementSibling}return!0}export{s as isDisabledReactIssue7711};
