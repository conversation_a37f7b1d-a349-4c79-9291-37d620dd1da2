// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.176.0
//   protoc               v3.19.1
// source: v1/weaviate.proto
import { AggregateReply, AggregateRequest } from './aggregate.js';
import { BatchObjectsReply, BatchObjectsRequest } from './batch.js';
import { BatchDeleteReply, BatchDeleteRequest } from './batch_delete.js';
import { SearchReply, SearchRequest } from './search_get.js';
import { TenantsGetReply, TenantsGetRequest } from './tenants.js';
export const protobufPackage = 'weaviate.v1';
export const WeaviateDefinition = {
  name: 'Weaviate',
  fullName: 'weaviate.v1.Weaviate',
  methods: {
    search: {
      name: 'Search',
      requestType: SearchRequest,
      requestStream: false,
      responseType: SearchReply,
      responseStream: false,
      options: {},
    },
    batchObjects: {
      name: 'BatchObjects',
      requestType: BatchObjectsRequest,
      requestStream: false,
      responseType: BatchObjectsReply,
      responseStream: false,
      options: {},
    },
    batchDelete: {
      name: 'BatchDelete',
      requestType: BatchDeleteRequest,
      requestStream: false,
      responseType: BatchDeleteReply,
      responseStream: false,
      options: {},
    },
    tenantsGet: {
      name: 'TenantsGet',
      requestType: TenantsGetRequest,
      requestStream: false,
      responseType: TenantsGetReply,
      responseStream: false,
      options: {},
    },
    aggregate: {
      name: 'Aggregate',
      requestType: AggregateRequest,
      requestStream: false,
      responseType: AggregateReply,
      responseStream: false,
      options: {},
    },
  },
};
