"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_HEADER_ALIGNMENT = exports.DEFAULT_ROW_ALIGNMENT = exports.DEFAULT_HEADER_FONT_COLOR = exports.DEFAULT_ROW_FONT_COLOR = exports.COLORS = exports.ALIGNMENTS = exports.DEFAULT_TABLE_STYLE = exports.DEFAULT_ROW_SEPARATOR = exports.DEFAULT_COLUMN_LEN = void 0;
exports.DEFAULT_COLUMN_LEN = 20;
exports.DEFAULT_ROW_SEPARATOR = false;
exports.DEFAULT_TABLE_STYLE = {
    /*
        Default Style
        ┌────────────┬─────┬──────┐
        │ foo        │ bar │ baz  │
        │ frobnicate │ bar │ quuz │
        └────────────┴─────┴──────┘
        */
    headerTop: {
        left: '┌',
        mid: '┬',
        right: '┐',
        other: '─',
    },
    headerBottom: {
        left: '├',
        mid: '┼',
        right: '┤',
        other: '─',
    },
    tableBottom: {
        left: '└',
        mid: '┴',
        right: '┘',
        other: '─',
    },
    vertical: '│',
    rowSeparator: {
        left: '├',
        mid: '┼',
        right: '┤',
        other: '─',
    },
};
exports.ALIGNMENTS = ['right', 'left', 'center'];
exports.COLORS = [
    'red',
    'green',
    'yellow',
    'white',
    'blue',
    'magenta',
    'cyan',
    'white_bold',
    'reset',
];
exports.DEFAULT_ROW_FONT_COLOR = 'white';
exports.DEFAULT_HEADER_FONT_COLOR = 'white_bold';
exports.DEFAULT_ROW_ALIGNMENT = 'right';
exports.DEFAULT_HEADER_ALIGNMENT = 'center';
