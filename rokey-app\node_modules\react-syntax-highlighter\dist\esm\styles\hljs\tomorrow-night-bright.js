export default {
  "hljs-comment": {
    "color": "#969896"
  },
  "hljs-quote": {
    "color": "#969896"
  },
  "hljs-variable": {
    "color": "#d54e53"
  },
  "hljs-template-variable": {
    "color": "#d54e53"
  },
  "hljs-tag": {
    "color": "#d54e53"
  },
  "hljs-name": {
    "color": "#d54e53"
  },
  "hljs-selector-id": {
    "color": "#d54e53"
  },
  "hljs-selector-class": {
    "color": "#d54e53"
  },
  "hljs-regexp": {
    "color": "#d54e53"
  },
  "hljs-deletion": {
    "color": "#d54e53"
  },
  "hljs-number": {
    "color": "#e78c45"
  },
  "hljs-built_in": {
    "color": "#e78c45"
  },
  "hljs-builtin-name": {
    "color": "#e78c45"
  },
  "hljs-literal": {
    "color": "#e78c45"
  },
  "hljs-type": {
    "color": "#e78c45"
  },
  "hljs-params": {
    "color": "#e78c45"
  },
  "hljs-meta": {
    "color": "#e78c45"
  },
  "hljs-link": {
    "color": "#e78c45"
  },
  "hljs-attribute": {
    "color": "#e7c547"
  },
  "hljs-string": {
    "color": "#b9ca4a"
  },
  "hljs-symbol": {
    "color": "#b9ca4a"
  },
  "hljs-bullet": {
    "color": "#b9ca4a"
  },
  "hljs-addition": {
    "color": "#b9ca4a"
  },
  "hljs-title": {
    "color": "#7aa6da"
  },
  "hljs-section": {
    "color": "#7aa6da"
  },
  "hljs-keyword": {
    "color": "#c397d8"
  },
  "hljs-selector-tag": {
    "color": "#c397d8"
  },
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "background": "black",
    "color": "#eaeaea",
    "padding": "0.5em"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  }
};