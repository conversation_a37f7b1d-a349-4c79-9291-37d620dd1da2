exports.id=5601,exports.ids=[5601],exports.modules={339:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function a(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function n(e,t,a,n,s){if("function"!=typeof a)throw TypeError("The listener must be a function");var o=new i(a,n||e,s),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function s(e,t){0==--e._eventsCount?e._events=new a:delete e._events[t]}function o(){this._events=new a,this._eventsCount=0}Object.create&&(a.prototype=Object.create(null),new a().__proto__||(r=!1)),o.prototype.eventNames=function(){var e,a,i=[];if(0===this._eventsCount)return i;for(a in e=this._events)t.call(e,a)&&i.push(r?a.slice(1):a);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},o.prototype.listeners=function(e){var t=r?r+e:e,a=this._events[t];if(!a)return[];if(a.fn)return[a.fn];for(var i=0,n=a.length,s=Array(n);i<n;i++)s[i]=a[i].fn;return s},o.prototype.listenerCount=function(e){var t=r?r+e:e,a=this._events[t];return a?a.fn?1:a.length:0},o.prototype.emit=function(e,t,a,i,n,s){var o=r?r+e:e;if(!this._events[o])return!1;var l,u,c=this._events[o],d=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),d){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,a),!0;case 4:return c.fn.call(c.context,t,a,i),!0;case 5:return c.fn.call(c.context,t,a,i,n),!0;case 6:return c.fn.call(c.context,t,a,i,n,s),!0}for(u=1,l=Array(d-1);u<d;u++)l[u-1]=arguments[u];c.fn.apply(c.context,l)}else{var h,p=c.length;for(u=0;u<p;u++)switch(c[u].once&&this.removeListener(e,c[u].fn,void 0,!0),d){case 1:c[u].fn.call(c[u].context);break;case 2:c[u].fn.call(c[u].context,t);break;case 3:c[u].fn.call(c[u].context,t,a);break;case 4:c[u].fn.call(c[u].context,t,a,i);break;default:if(!l)for(h=1,l=Array(d-1);h<d;h++)l[h-1]=arguments[h];c[u].fn.apply(c[u].context,l)}}return!0},o.prototype.on=function(e,t,r){return n(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return n(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,a,i){var n=r?r+e:e;if(!this._events[n])return this;if(!t)return s(this,n),this;var o=this._events[n];if(o.fn)o.fn!==t||i&&!o.once||a&&o.context!==a||s(this,n);else{for(var l=0,u=[],c=o.length;l<c;l++)(o[l].fn!==t||i&&!o[l].once||a&&o[l].context!==a)&&u.push(o[l]);u.length?this._events[n]=1===u.length?u[0]:u:s(this,n)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&s(this,t)):(this._events=new a,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o},2843:e=>{"use strict";class t{constructor(){this.max=1e3,this.map=new Map}get(e){let t=this.map.get(e);if(void 0!==t)return this.map.delete(e),this.map.set(e,t),t}delete(e){return this.map.delete(e)}set(e,t){if(!this.delete(e)&&void 0!==t){if(this.map.size>=this.max){let e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}e.exports=t},3706:(e,t,r)=>{"use strict";let a=/\s+/g;class i{constructor(e,t){if(t=s(t),e instanceof i)if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;else return new i(e.raw,t);if(e instanceof o)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().replace(a," "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let e=this.set[0];if(this.set=this.set.filter(e=>!y(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1){for(let e of this.set)if(1===e.length&&b(e[0])){this.set=[e];break}}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let t=this.set[e];for(let e=0;e<t.length;e++)e>0&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let t=((this.options.includePrerelease&&m)|(this.options.loose&&g))+":"+e,r=n.get(t);if(r)return r;let a=this.options.loose,i=a?c[d.HYPHENRANGELOOSE]:c[d.HYPHENRANGE];l("hyphen replace",e=e.replace(i,x(this.options.includePrerelease))),l("comparator trim",e=e.replace(c[d.COMPARATORTRIM],h)),l("tilde trim",e=e.replace(c[d.TILDETRIM],p)),l("caret trim",e=e.replace(c[d.CARETTRIM],f));let s=e.split(" ").map(e=>v(e,this.options)).join(" ").split(/\s+/).map(e=>T(e,this.options));a&&(s=s.filter(e=>(l("loose invalid filter",e,this.options),!!e.match(c[d.COMPARATORLOOSE])))),l("range list",s);let u=new Map;for(let e of s.map(e=>new o(e,this.options))){if(y(e))return[e];u.set(e.value,e)}u.size>1&&u.has("")&&u.delete("");let b=[...u.values()];return n.set(t,b),b}intersects(e,t){if(!(e instanceof i))throw TypeError("a Range is required");return this.set.some(r=>_(r,t)&&e.set.some(e=>_(e,t)&&r.every(r=>e.every(e=>r.intersects(e,t)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new u(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(j(this.set[t],e,this.options))return!0;return!1}}e.exports=i;let n=new(r(2843)),s=r(98300),o=r(14239),l=r(38267),u=r(64487),{safeRe:c,t:d,comparatorTrimReplace:h,tildeTrimReplace:p,caretTrimReplace:f}=r(26515),{FLAG_INCLUDE_PRERELEASE:m,FLAG_LOOSE:g}=r(32397),y=e=>"<0.0.0-0"===e.value,b=e=>""===e.value,_=(e,t)=>{let r=!0,a=e.slice(),i=a.pop();for(;r&&a.length;)r=a.every(e=>i.intersects(e,t)),i=a.pop();return r},v=(e,t)=>(l("comp",e,t),l("caret",e=$(e,t)),l("tildes",e=E(e,t)),l("xrange",e=S(e,t)),l("stars",e=A(e,t)),e),w=e=>!e||"x"===e.toLowerCase()||"*"===e,E=(e,t)=>e.trim().split(/\s+/).map(e=>O(e,t)).join(" "),O=(e,t)=>{let r=t.loose?c[d.TILDELOOSE]:c[d.TILDE];return e.replace(r,(t,r,a,i,n)=>{let s;return l("tilde",e,t,r,a,i,n),w(r)?s="":w(a)?s=`>=${r}.0.0 <${+r+1}.0.0-0`:w(i)?s=`>=${r}.${a}.0 <${r}.${+a+1}.0-0`:n?(l("replaceTilde pr",n),s=`>=${r}.${a}.${i}-${n} <${r}.${+a+1}.0-0`):s=`>=${r}.${a}.${i} <${r}.${+a+1}.0-0`,l("tilde return",s),s})},$=(e,t)=>e.trim().split(/\s+/).map(e=>I(e,t)).join(" "),I=(e,t)=>{l("caret",e,t);let r=t.loose?c[d.CARETLOOSE]:c[d.CARET],a=t.includePrerelease?"-0":"";return e.replace(r,(t,r,i,n,s)=>{let o;return l("caret",e,t,r,i,n,s),w(r)?o="":w(i)?o=`>=${r}.0.0${a} <${+r+1}.0.0-0`:w(n)?o="0"===r?`>=${r}.${i}.0${a} <${r}.${+i+1}.0-0`:`>=${r}.${i}.0${a} <${+r+1}.0.0-0`:s?(l("replaceCaret pr",s),o="0"===r?"0"===i?`>=${r}.${i}.${n}-${s} <${r}.${i}.${+n+1}-0`:`>=${r}.${i}.${n}-${s} <${r}.${+i+1}.0-0`:`>=${r}.${i}.${n}-${s} <${+r+1}.0.0-0`):(l("no pr"),o="0"===r?"0"===i?`>=${r}.${i}.${n}${a} <${r}.${i}.${+n+1}-0`:`>=${r}.${i}.${n}${a} <${r}.${+i+1}.0-0`:`>=${r}.${i}.${n} <${+r+1}.0.0-0`),l("caret return",o),o})},S=(e,t)=>(l("replaceXRanges",e,t),e.split(/\s+/).map(e=>k(e,t)).join(" ")),k=(e,t)=>{e=e.trim();let r=t.loose?c[d.XRANGELOOSE]:c[d.XRANGE];return e.replace(r,(r,a,i,n,s,o)=>{l("xRange",e,r,a,i,n,s,o);let u=w(i),c=u||w(n),d=c||w(s);return"="===a&&d&&(a=""),o=t.includePrerelease?"-0":"",u?r=">"===a||"<"===a?"<0.0.0-0":"*":a&&d?(c&&(n=0),s=0,">"===a?(a=">=",c?(i=+i+1,n=0):n=+n+1,s=0):"<="===a&&(a="<",c?i=+i+1:n=+n+1),"<"===a&&(o="-0"),r=`${a+i}.${n}.${s}${o}`):c?r=`>=${i}.0.0${o} <${+i+1}.0.0-0`:d&&(r=`>=${i}.${n}.0${o} <${i}.${+n+1}.0-0`),l("xRange return",r),r})},A=(e,t)=>(l("replaceStars",e,t),e.trim().replace(c[d.STAR],"")),T=(e,t)=>(l("replaceGTE0",e,t),e.trim().replace(c[t.includePrerelease?d.GTE0PRE:d.GTE0],"")),x=e=>(t,r,a,i,n,s,o,l,u,c,d,h)=>(r=w(a)?"":w(i)?`>=${a}.0.0${e?"-0":""}`:w(n)?`>=${a}.${i}.0${e?"-0":""}`:s?`>=${r}`:`>=${r}${e?"-0":""}`,l=w(u)?"":w(c)?`<${+u+1}.0.0-0`:w(d)?`<${u}.${+c+1}.0-0`:h?`<=${u}.${c}.${d}-${h}`:e?`<${u}.${c}.${+d+1}-0`:`<=${l}`,`${r} ${l}`.trim()),j=(e,t,r)=>{for(let r=0;r<e.length;r++)if(!e[r].test(t))return!1;if(t.prerelease.length&&!r.includePrerelease){for(let r=0;r<e.length;r++)if(l(e[r].semver),e[r].semver!==o.ANY&&e[r].semver.prerelease.length>0){let a=e[r].semver;if(a.major===t.major&&a.minor===t.minor&&a.patch===t.patch)return!0}return!1}return!0}},4346:(e,t,r)=>{"use strict";r.d(t,{X0:()=>s,hr:()=>i,pH:()=>n});let a=Symbol.for("ls:tracing_async_local_storage"),i=Symbol.for("lc:context_variables"),n=e=>{globalThis[a]=e},s=()=>globalThis[a]},5286:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(77598),i=r.n(a);let n=new Uint8Array(256),s=n.length;function o(){return s>n.length-16&&(i().randomFillSync(n),s=0),n.slice(s,s+=16)}},7110:(e,t,r)=>{"use strict";let a=r(58361);e.exports=(e,t)=>{let r=a(e,t);return r&&r.prerelease.length?r.prerelease:null}},8212:(e,t,r)=>{"use strict";r.d(t,{q:()=>i});var a=r(59438);let i=()=>(0,a.Jz)("PROJECT")??(0,a.Az)("LANGCHAIN_SESSION")??"default"},8536:(e,t,r)=>{"use strict";let a=r(24800);e.exports=(e,t)=>e.sort((e,r)=>a(r,e,t))},11337:(e,t,r)=>{"use strict";let a=r(3706),i=r(14239),{ANY:n}=i,s=r(42679),o=r(33877),l=[new i(">=0.0.0-0")],u=[new i(">=0.0.0")],c=(e,t,r)=>{let a,i,c,p,f,m,g;if(e===t)return!0;if(1===e.length&&e[0].semver===n)if(1===t.length&&t[0].semver===n)return!0;else e=r.includePrerelease?l:u;if(1===t.length&&t[0].semver===n)if(r.includePrerelease)return!0;else t=u;let y=new Set;for(let t of e)">"===t.operator||">="===t.operator?a=d(a,t,r):"<"===t.operator||"<="===t.operator?i=h(i,t,r):y.add(t.semver);if(y.size>1)return null;if(a&&i&&((c=o(a.semver,i.semver,r))>0||0===c&&(">="!==a.operator||"<="!==i.operator)))return null;for(let e of y){if(a&&!s(e,String(a),r)||i&&!s(e,String(i),r))return null;for(let a of t)if(!s(e,String(a),r))return!1;return!0}let b=!!i&&!r.includePrerelease&&!!i.semver.prerelease.length&&i.semver,_=!!a&&!r.includePrerelease&&!!a.semver.prerelease.length&&a.semver;for(let e of(b&&1===b.prerelease.length&&"<"===i.operator&&0===b.prerelease[0]&&(b=!1),t)){if(g=g||">"===e.operator||">="===e.operator,m=m||"<"===e.operator||"<="===e.operator,a){if(_&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===_.major&&e.semver.minor===_.minor&&e.semver.patch===_.patch&&(_=!1),">"===e.operator||">="===e.operator){if((p=d(a,e,r))===e&&p!==a)return!1}else if(">="===a.operator&&!s(a.semver,String(e),r))return!1}if(i){if(b&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===b.major&&e.semver.minor===b.minor&&e.semver.patch===b.patch&&(b=!1),"<"===e.operator||"<="===e.operator){if((f=h(i,e,r))===e&&f!==i)return!1}else if("<="===i.operator&&!s(i.semver,String(e),r))return!1}if(!e.operator&&(i||a)&&0!==c)return!1}return(!a||!m||!!i||0===c)&&(!i||!g||!!a||0===c)&&!_&&!b&&!0},d=(e,t,r)=>{if(!e)return t;let a=o(e.semver,t.semver,r);return a>0?e:a<0||">"===t.operator&&">="===e.operator?t:e},h=(e,t,r)=>{if(!e)return t;let a=o(e.semver,t.semver,r);return a<0?e:a>0||"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,r={})=>{if(e===t)return!0;e=new a(e,r),t=new a(t,r);let i=!1;e:for(let a of e.set){for(let e of t.set){let t=c(a,e,r);if(i=i||null!==t,t)continue e}if(i)return!1}return!0}},11457:(e,t,r)=>{"use strict";r.d(t,{gk:()=>h,m5:()=>p});var a=r(82116),i=r(98431),n=r(65381),s=r(59438);let o=e=>void 0!==e?e:!!["TRACING_V2","TRACING"].find(e=>"true"===(0,s.Jz)(e)),l=Symbol.for("lc:context_variables");var u=r(8212),c=r(74081);class d{constructor(e,t,r,a){Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"project_name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"replicas",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.metadata=e,this.tags=t,this.project_name=r,this.replicas=a}static fromHeader(e){let t,r,a=e.split(","),i={},n=[];for(let e of a){let[a,s]=e.split("="),o=decodeURIComponent(s);"langsmith-metadata"===a?i=JSON.parse(o):"langsmith-tags"===a?n=o.split(","):"langsmith-project"===a?t=o:"langsmith-replicas"===a&&(r=JSON.parse(o))}return new d(i,n,t,r)}toHeader(){let e=[];return this.metadata&&Object.keys(this.metadata).length>0&&e.push(`langsmith-metadata=${encodeURIComponent(JSON.stringify(this.metadata))}`),this.tags&&this.tags.length>0&&e.push(`langsmith-tags=${encodeURIComponent(this.tags.join(","))}`),this.project_name&&e.push(`langsmith-project=${encodeURIComponent(this.project_name)}`),e.join(",")}}class h{constructor(e){if(Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"run_type",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"project_name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"parent_run",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"child_runs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"start_time",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"end_time",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"extra",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"error",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"serialized",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"inputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"reference_example_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"events",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"trace_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"dotted_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tracingEnabled",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"execution_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"child_execution_order",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"attachments",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"replicas",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),p(e))return void Object.assign(this,{...e});let t=h.getDefaultConfig(),{metadata:r,...a}=e,i=a.client??h.getSharedClient(),n={...r,...a?.extra?.metadata};if(a.extra={...a.extra,metadata:n},Object.assign(this,{...t,...a,client:i}),this.trace_id||(this.parent_run?this.trace_id=this.parent_run.trace_id??this.id:this.trace_id=this.id),this.execution_order??=1,this.child_execution_order??=1,!this.dotted_order){let e=function(e,t,r=1){let a=r.toFixed(0).slice(0,3).padStart(3,"0");return`${new Date(e).toISOString().slice(0,-1)}${a}Z`.replace(/[-:.]/g,"")+t}(this.start_time,this.id,this.execution_order);this.parent_run?this.dotted_order=this.parent_run.dotted_order+"."+e:this.dotted_order=e}}set metadata(e){this.extra={...this.extra,metadata:{...this.extra?.metadata,...e}}}get metadata(){return this.extra?.metadata}static getDefaultConfig(){return{id:a.A(),run_type:"chain",project_name:(0,u.q)(),child_runs:[],api_url:(0,s.Az)("LANGCHAIN_ENDPOINT")??"http://localhost:1984",api_key:(0,s.Az)("LANGCHAIN_API_KEY"),caller_options:{},start_time:Date.now(),serialized:{},inputs:{},extra:{}}}static getSharedClient(){return h.sharedClient||(h.sharedClient=new n.Kj),h.sharedClient}createChild(e){var t,r;let a=this.child_execution_order+1,i=new h({...e,parent_run:this,project_name:this.project_name,replicas:this.replicas,client:this.client,tracingEnabled:this.tracingEnabled,execution_order:a,child_execution_order:a});l in this&&(i[l]=this[l]);let n=Symbol.for("lc:child_config"),s=e.extra?.[n]??this.extra[n];if(void 0!==(t=s)&&"object"==typeof t.callbacks&&(m(t.callbacks?.handlers)||m(t.callbacks))){let e={...s},t="object"==typeof(r=e.callbacks)&&null!=r&&Array.isArray(r.handlers)?e.callbacks.copy?.():void 0;t&&(Object.assign(t,{_parentRunId:i.id}),t.handlers?.find(f)?.updateFromRunTree?.(i),e.callbacks=t),i.extra[n]=e}let o=new Set,u=this;for(;null!=u&&!o.has(u.id);)o.add(u.id),u.child_execution_order=Math.max(u.child_execution_order,a),u=u.parent_run;return this.child_runs.push(i),i}async end(e,t,r=Date.now(),a){this.outputs=this.outputs??e,this.error=this.error??t,this.end_time=this.end_time??r,a&&Object.keys(a).length>0&&(this.extra=this.extra?{...this.extra,metadata:{...this.extra.metadata,...a}}:{metadata:a})}_convertToCreate(e,t,r=!0){let a,i,n=e.extra??{};if(n?.runtime?.library===void 0&&(n.runtime||(n.runtime={}),t))for(let[e,r]of Object.entries(t))n.runtime[e]||(n.runtime[e]=r);return r?(i=e.parent_run?.id,a=[]):(a=e.child_runs.map(e=>this._convertToCreate(e,t,r)),i=void 0),{id:e.id,name:e.name,start_time:e.start_time,end_time:e.end_time,run_type:e.run_type,reference_example_id:e.reference_example_id,extra:n,serialized:e.serialized,error:e.error,inputs:e.inputs,outputs:e.outputs,session_name:e.project_name,child_runs:a,parent_run_id:i,trace_id:e.trace_id,dotted_order:e.dotted_order,tags:e.tags,attachments:e.attachments,events:e.events}}_remapForProject(e,t,r=!0){let a,n=this._convertToCreate(this,t,r);if(e===this.project_name)return n;let s=t=>i.A(`${t}:${e}`,i.A.DNS),o=s(n.id),l=n.trace_id?s(n.trace_id):void 0,u=n.parent_run_id?s(n.parent_run_id):void 0;if(n.dotted_order){let e=n.dotted_order.split(".").map(e=>{let t=e.slice(0,-36),r=e.slice(-36),a=parseInt(t.slice(0,4)),i=parseInt(t.slice(4,6))-1,n=parseInt(t.slice(6,8)),s=parseInt(t.slice(9,11)),o=parseInt(t.slice(11,13));return[new Date(a,i,n,s,o,parseInt(t.slice(13,15)),parseInt(t.slice(15,21))/1e3),r]}),t=[];for(let r=0;r<e.length-1;r++){let[a,i]=e[r],n=s(i);t.push(a.toISOString().replace(/[-:]/g,"").replace(".","")+n)}let[r]=e[e.length-1];t.push(r.toISOString().replace(/[-:]/g,"").replace(".","")+o),a=t.join(".")}else a=void 0;return{...n,id:o,trace_id:l,parent_run_id:u,dotted_order:a,session_name:e}}async postRun(e=!0){try{let t=(0,s.Ec)();if(this.replicas&&this.replicas.length>0)for(let[e]of this.replicas){let r=this._remapForProject(e,t,!0);await this.client.createRun(r)}else{let r=this._convertToCreate(this,t,e);await this.client.createRun(r)}if(!e)for(let e of((0,c.m)("Posting with excludeChildRuns=false is deprecated and will be removed in a future version."),this.child_runs))await e.postRun(!1)}catch(e){console.error(`Error in postRun for run ${this.id}:`,e)}}async patchRun(){if(this.replicas&&this.replicas.length>0)for(let[e,t]of this.replicas){let r=this._remapForProject(e);await this.client.updateRun(r.id,{inputs:r.inputs,outputs:r.outputs,error:r.error,parent_run_id:r.parent_run_id,session_name:r.session_name,reference_example_id:r.reference_example_id,end_time:r.end_time,dotted_order:r.dotted_order,trace_id:r.trace_id,events:r.events,tags:r.tags,extra:r.extra,attachments:this.attachments,...t})}else try{let e={end_time:this.end_time,error:this.error,inputs:this.inputs,outputs:this.outputs,parent_run_id:this.parent_run?.id,reference_example_id:this.reference_example_id,extra:this.extra,events:this.events,dotted_order:this.dotted_order,trace_id:this.trace_id,tags:this.tags,attachments:this.attachments,session_name:this.project_name};await this.client.updateRun(this.id,e)}catch(e){console.error(`Error in patchRun for run ${this.id}`,e)}}toJSON(){return this._convertToCreate(this,void 0,!1)}addEvent(e){this.events||(this.events=[]),"string"==typeof e?this.events.push({name:"event",time:new Date().toISOString(),message:e}):this.events.push({...e,time:e.time??new Date().toISOString()})}static fromRunnableConfig(e,t){let r,a,i,n=e?.callbacks,s=o();if(n){let e=n?.getParentRunId?.()??"",t=n?.handlers?.find(e=>e?.name=="langchain_tracer");r=t?.getRun?.(e),a=t?.projectName,i=t?.client,s=s||!!t}return r?new h({name:r.name,id:r.id,trace_id:r.trace_id,dotted_order:r.dotted_order,client:i,tracingEnabled:s,project_name:a,tags:[...new Set((r?.tags??[]).concat(e?.tags??[]))],extra:{metadata:{...r?.extra?.metadata,...e?.metadata}}}).createChild(t):new h({...t,client:i,tracingEnabled:s,project_name:a})}static fromDottedOrder(e){return this.fromHeaders({"langsmith-trace":e})}static fromHeaders(e,t){let r="get"in e&&"function"==typeof e.get?{"langsmith-trace":e.get("langsmith-trace"),baggage:e.get("baggage")}:e,a=r["langsmith-trace"];if(!a||"string"!=typeof a)return;let i=a.trim(),n=i.split(".").map(e=>{let[t,r]=e.split("Z");return{strTime:t,time:Date.parse(t+"Z"),uuid:r}}),s=n[0].uuid,o={...t,name:t?.name??"parent",run_type:t?.run_type??"chain",start_time:t?.start_time??Date.now(),id:n.at(-1)?.uuid,trace_id:s,dotted_order:i};if(r.baggage&&"string"==typeof r.baggage){let e=d.fromHeader(r.baggage);o.metadata=e.metadata,o.tags=e.tags,o.project_name=e.project_name,o.replicas=e.replicas}return new h(o)}toHeaders(e){let t={"langsmith-trace":this.dotted_order,baggage:new d(this.extra?.metadata,this.tags,this.project_name,this.replicas).toHeader()};if(e)for(let[r,a]of Object.entries(t))e.set(r,a);return t}}function p(e){return void 0!==e&&"function"==typeof e.createChild&&"function"==typeof e.postRun}function f(e){return"object"==typeof e&&null!=e&&"string"==typeof e.name&&"langchain_tracer"===e.name}function m(e){return Array.isArray(e)&&e.some(e=>f(e))}Object.defineProperty(h,"sharedClient",{enumerable:!0,configurable:!0,writable:!0,value:null})},12441:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let a=r(71611);class i{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let i=a.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},13250:(e,t,r)=>{"use strict";function a(e,t=i){let r=(e=e.trim()).indexOf("```");if(-1===r)return t(e);let n=e.substring(r+3);n.startsWith("json\n")?n=n.substring(5):n.startsWith("json")?n=n.substring(4):n.startsWith("\n")&&(n=n.substring(1));let s=n.indexOf("```"),o=n;return -1!==s&&(o=n.substring(0,s)),t(o.trim())}function i(e){if(void 0===e)return null;try{return JSON.parse(e)}catch(e){}let t="",r=[],a=!1,i=!1;for(let n of e){if(a)'"'!==n||i?"\n"!==n||i?i="\\"===n&&!i:n="\\n":a=!1;else if('"'===n)a=!0,i=!1;else if("{"===n)r.push("}");else if("["===n)r.push("]");else if("}"===n||"]"===n)if(!r||r[r.length-1]!==n)return null;else r.pop();t+=n}a&&(t+='"');for(let e=r.length-1;e>=0;e-=1)t+=r[e];try{return JSON.parse(t)}catch(e){return null}}r.d(t,{D:()=>a,d:()=>i})},13346:(e,t,r)=>{"use strict";function a(e,t){return e.lc_error_code=t,e.message=`${e.message}

Troubleshooting URL: https://js.langchain.com/docs/troubleshooting/errors/${t}/
`,e}r.d(t,{Y:()=>a})},14239:(e,t,r)=>{"use strict";let a=Symbol("SemVer ANY");class i{static get ANY(){return a}constructor(e,t){if(t=n(t),e instanceof i)if(!!t.loose===e.loose)return e;else e=e.value;u("comparator",e=e.trim().split(/\s+/).join(" "),t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===a?this.value="":this.value=this.operator+this.semver.version,u("comp",this)}parse(e){let t=this.options.loose?s[o.COMPARATORLOOSE]:s[o.COMPARATOR],r=e.match(t);if(!r)throw TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==r[1]?r[1]:"","="===this.operator&&(this.operator=""),r[2]?this.semver=new c(r[2],this.options.loose):this.semver=a}toString(){return this.value}test(e){if(u("Comparator.test",e,this.options.loose),this.semver===a||e===a)return!0;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof i))throw TypeError("a Comparator is required");return""===this.operator?""===this.value||new d(e.value,t).test(this.value):""===e.operator?""===e.value||new d(this.value,t).test(e.semver):!((t=n(t)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||l(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||l(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">"))}}e.exports=i;let n=r(98300),{safeRe:s,t:o}=r(26515),l=r(84450),u=r(38267),c=r(64487),d=r(3706)},15641:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AsyncCaller:()=>o});var a=r(63611),i=r(71719);let n=[400,401,402,403,404,405,406,407,409],s=e=>{if(e.message.startsWith("Cancel")||e.message.startsWith("AbortError")||"AbortError"===e.name||e?.code==="ECONNABORTED")throw e;let t=e?.response?.status??e?.status;if(t&&n.includes(+t))throw e;if(e?.error?.code==="insufficient_quota"){let t=Error(e?.message);throw t.name="InsufficientQuotaError",t}};class o{constructor(e){Object.defineProperty(this,"maxConcurrency",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"maxRetries",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"onFailedAttempt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"queue",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxConcurrency=e.maxConcurrency??1/0,this.maxRetries=e.maxRetries??6,this.onFailedAttempt=e.onFailedAttempt??s;let t=i.default;this.queue=new t({concurrency:this.maxConcurrency})}call(e,...t){return this.queue.add(()=>a(()=>e(...t).catch(e=>{if(e instanceof Error)throw e;throw Error(e)}),{onFailedAttempt:this.onFailedAttempt,retries:this.maxRetries,randomize:!0}),{throwOnTimeout:!0})}callWithOptions(e,t,...r){return e.signal?Promise.race([this.call(t,...r),new Promise((t,r)=>{e.signal?.addEventListener("abort",()=>{r(Error("AbortError"))})})]):this.call(t,...r)}fetch(...e){return this.call(()=>fetch(...e).then(e=>e.ok?e:Promise.reject(e)))}}},16275:(e,t,r)=>{"use strict";r.d(t,{H:()=>u,KX:()=>o,Od:()=>s,jm:()=>l});var a=r(13250),i=r(72892),n=r(28895);class s extends i.XQ{get lc_aliases(){return{...super.lc_aliases,tool_calls:"tool_calls",invalid_tool_calls:"invalid_tool_calls"}}constructor(e,t){let r;if("string"==typeof e)r={content:e,tool_calls:[],invalid_tool_calls:[],additional_kwargs:t??{}};else{r=e;let t=r.additional_kwargs?.tool_calls,a=r.tool_calls;null!=t&&t.length>0&&(void 0===a||0===a.length)&&console.warn("New LangChain packages are available that more efficiently handle tool calling.\n\nPlease upgrade your packages to versions that set message tool calls. e.g., `yarn add @langchain/anthropic`, yarn add @langchain/openai`, etc.");try{if(null!=t&&void 0===a){let[e,a]=(0,n.p1)(t);r.tool_calls=e??[],r.invalid_tool_calls=a??[]}else r.tool_calls=r.tool_calls??[],r.invalid_tool_calls=r.invalid_tool_calls??[]}catch(e){r.tool_calls=[],r.invalid_tool_calls=[]}}super(r),Object.defineProperty(this,"tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"invalid_tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"usage_metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),"string"!=typeof r&&(this.tool_calls=r.tool_calls??this.tool_calls,this.invalid_tool_calls=r.invalid_tool_calls??this.invalid_tool_calls),this.usage_metadata=r.usage_metadata}static lc_name(){return"AIMessage"}_getType(){return"ai"}get _printableFields(){return{...super._printableFields,tool_calls:this.tool_calls,invalid_tool_calls:this.invalid_tool_calls,usage_metadata:this.usage_metadata}}}function o(e){return"ai"===e._getType()}function l(e){return"ai"===e._getType()}class u extends i.gj{constructor(e){let t;if("string"==typeof e)t={content:e,tool_calls:[],invalid_tool_calls:[],tool_call_chunks:[]};else if(void 0===e.tool_call_chunks)t={...e,tool_calls:e.tool_calls??[],invalid_tool_calls:[],tool_call_chunks:[],usage_metadata:void 0!==e.usage_metadata?e.usage_metadata:void 0};else{let r=[],i=[];for(let t of e.tool_call_chunks){let e={};try{if(e=(0,a.d)(t.args||"{}"),null===e||"object"!=typeof e||Array.isArray(e))throw Error("Malformed tool call chunk args.");r.push({name:t.name??"",args:e,id:t.id,type:"tool_call"})}catch(e){i.push({name:t.name,args:t.args,id:t.id,error:"Malformed args.",type:"invalid_tool_call"})}}t={...e,tool_calls:r,invalid_tool_calls:i,usage_metadata:void 0!==e.usage_metadata?e.usage_metadata:void 0}}super(t),Object.defineProperty(this,"tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"invalid_tool_calls",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"tool_call_chunks",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"usage_metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.tool_call_chunks=t.tool_call_chunks??this.tool_call_chunks,this.tool_calls=t.tool_calls??this.tool_calls,this.invalid_tool_calls=t.invalid_tool_calls??this.invalid_tool_calls,this.usage_metadata=t.usage_metadata}get lc_aliases(){return{...super.lc_aliases,tool_calls:"tool_calls",invalid_tool_calls:"invalid_tool_calls",tool_call_chunks:"tool_call_chunks"}}static lc_name(){return"AIMessageChunk"}_getType(){return"ai"}get _printableFields(){return{...super._printableFields,tool_calls:this.tool_calls,tool_call_chunks:this.tool_call_chunks,invalid_tool_calls:this.invalid_tool_calls,usage_metadata:this.usage_metadata}}concat(e){let t={content:(0,i._I)(this.content,e.content),additional_kwargs:(0,i.ns)(this.additional_kwargs,e.additional_kwargs),response_metadata:(0,i.ns)(this.response_metadata,e.response_metadata),tool_call_chunks:[],id:this.id??e.id};if(void 0!==this.tool_call_chunks||void 0!==e.tool_call_chunks){let r=(0,i.Vt)(this.tool_call_chunks,e.tool_call_chunks);void 0!==r&&r.length>0&&(t.tool_call_chunks=r)}if(void 0!==this.usage_metadata||void 0!==e.usage_metadata){let r={...(this.usage_metadata?.input_token_details?.audio!==void 0||e.usage_metadata?.input_token_details?.audio!==void 0)&&{audio:(this.usage_metadata?.input_token_details?.audio??0)+(e.usage_metadata?.input_token_details?.audio??0)},...(this.usage_metadata?.input_token_details?.cache_read!==void 0||e.usage_metadata?.input_token_details?.cache_read!==void 0)&&{cache_read:(this.usage_metadata?.input_token_details?.cache_read??0)+(e.usage_metadata?.input_token_details?.cache_read??0)},...(this.usage_metadata?.input_token_details?.cache_creation!==void 0||e.usage_metadata?.input_token_details?.cache_creation!==void 0)&&{cache_creation:(this.usage_metadata?.input_token_details?.cache_creation??0)+(e.usage_metadata?.input_token_details?.cache_creation??0)}},a={...(this.usage_metadata?.output_token_details?.audio!==void 0||e.usage_metadata?.output_token_details?.audio!==void 0)&&{audio:(this.usage_metadata?.output_token_details?.audio??0)+(e.usage_metadata?.output_token_details?.audio??0)},...(this.usage_metadata?.output_token_details?.reasoning!==void 0||e.usage_metadata?.output_token_details?.reasoning!==void 0)&&{reasoning:(this.usage_metadata?.output_token_details?.reasoning??0)+(e.usage_metadata?.output_token_details?.reasoning??0)}},i=this.usage_metadata??{input_tokens:0,output_tokens:0,total_tokens:0},n=e.usage_metadata??{input_tokens:0,output_tokens:0,total_tokens:0};t.usage_metadata={input_tokens:i.input_tokens+n.input_tokens,output_tokens:i.output_tokens+n.output_tokens,total_tokens:i.total_tokens+n.total_tokens,...Object.keys(r).length>0&&{input_token_details:r},...Object.keys(a).length>0&&{output_token_details:a}}}return new u(t)}}},17107:(e,t,r)=>{"use strict";var a;function i(e,t){let r=typeof e;if(r!==typeof t)return!1;if(Array.isArray(e)){if(!Array.isArray(t))return!1;let r=e.length;if(r!==t.length)return!1;for(let a=0;a<r;a++)if(!i(e[a],t[a]))return!1;return!0}if("object"===r){if(!e||!t)return e===t;let r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;for(let a of r)if(!i(e[a],t[a]))return!1;return!0}return e===t}function n(e){return encodeURI(e.replace(/~/g,"~0").replace(/\//g,"~1"))}r.d(t,{Dr:()=>O,rA:()=>i,DS:()=>c,tf:()=>E});let s={prefixItems:!0,items:!0,allOf:!0,anyOf:!0,oneOf:!0},o={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependentSchemas:!0},l={id:!0,$id:!0,$ref:!0,$schema:!0,$anchor:!0,$vocabulary:!0,$comment:!0,default:!0,enum:!0,const:!0,required:!0,type:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0},u="undefined"!=typeof self&&self.location&&"null"!==self.location.origin?new URL(self.location.origin+self.location.pathname+location.search):new URL("https://github.com/cfworker");function c(e,t=Object.create(null),r=u,a=""){if(e&&"object"==typeof e&&!Array.isArray(e)){let i=e.$id||e.id;if(i){let n=new URL(i,r.href);n.hash.length>1?t[n.href]=e:(n.hash="",""===a?r=n:c(e,t,r))}}else if(!0!==e&&!1!==e)return t;let i=r.href+(a?"#"+a:"");if(void 0!==t[i])throw Error(`Duplicate schema URI "${i}".`);if(t[i]=e,!0===e||!1===e)return t;if(void 0===e.__absolute_uri__&&Object.defineProperty(e,"__absolute_uri__",{enumerable:!1,value:i}),e.$ref&&void 0===e.__absolute_ref__){let t=new URL(e.$ref,r.href);t.hash=t.hash,Object.defineProperty(e,"__absolute_ref__",{enumerable:!1,value:t.href})}if(e.$recursiveRef&&void 0===e.__absolute_recursive_ref__){let t=new URL(e.$recursiveRef,r.href);t.hash=t.hash,Object.defineProperty(e,"__absolute_recursive_ref__",{enumerable:!1,value:t.href})}for(let i in e.$anchor&&(t[new URL("#"+e.$anchor,r.href).href]=e),e){if(l[i])continue;let u=`${a}/${n(i)}`,d=e[i];if(Array.isArray(d)){if(s[i]){let e=d.length;for(let a=0;a<e;a++)c(d[a],t,r,`${u}/${a}`)}}else if(o[i])for(let e in d)c(d[e],t,r,`${u}/${n(e)}`);else c(d,t,r,u)}return t}let d=/^(\d\d\d\d)-(\d\d)-(\d\d)$/,h=[0,31,28,31,30,31,30,31,31,30,31,30,31],p=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d(?::?\d\d)?)?$/i;function f(e){return e.test.bind(e)}let m={date:g,time:y.bind(void 0,!1),"date-time":function(e){let t=e.split(b);return 2==t.length&&g(t[0])&&y(!0,t[1])},duration:e=>e.length>1&&e.length<80&&(/^P\d+([.,]\d+)?W$/.test(e)||/^P[\dYMDTHS]*(\d[.,]\d+)?[YMDHS]$/.test(e)&&/^P([.,\d]+Y)?([.,\d]+M)?([.,\d]+D)?(T([.,\d]+H)?([.,\d]+M)?([.,\d]+S)?)?$/.test(e)),uri:function(e){return _.test(e)&&v.test(e)},"uri-reference":f(/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i),"uri-template":f(/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i),url:f(/^(?:(?:https?|ftp):\/\/)(?:\S+(?::\S*)?@)?(?:(?!10(?:\.\d{1,3}){3})(?!127(?:\.\d{1,3}){3})(?!169\.254(?:\.\d{1,3}){2})(?!192\.168(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u{00a1}-\u{ffff}0-9]+-?)*[a-z\u{00a1}-\u{ffff}0-9]+)(?:\.(?:[a-z\u{00a1}-\u{ffff}0-9]+-?)*[a-z\u{00a1}-\u{ffff}0-9]+)*(?:\.(?:[a-z\u{00a1}-\u{ffff}]{2,})))(?::\d{2,5})?(?:\/[^\s]*)?$/iu),email:e=>{if('"'===e[0])return!1;let[t,r,...a]=e.split("@");return!(!t||!r||0!==a.length||t.length>64||r.length>253||"."===t[0]||t.endsWith(".")||t.includes(".."))&&!!/^[a-z0-9.-]+$/i.test(r)&&!!/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+$/i.test(t)&&r.split(".").every(e=>/^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$/i.test(e))},hostname:f(/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i),ipv4:f(/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/),ipv6:f(/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i),regex:function(e){if(w.test(e))return!1;try{return RegExp(e,"u"),!0}catch(e){return!1}},uuid:f(/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i),"json-pointer":f(/^(?:\/(?:[^~/]|~0|~1)*)*$/),"json-pointer-uri-fragment":f(/^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i),"relative-json-pointer":f(/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/)};function g(e){var t;let r=e.match(d);if(!r)return!1;let a=+r[1],i=+r[2],n=+r[3];return i>=1&&i<=12&&n>=1&&n<=(2==i&&(t=a)%4==0&&(t%100!=0||t%400==0)?29:h[i])}function y(e,t){let r=t.match(p);if(!r)return!1;let a=+r[1],i=+r[2],n=+r[3],s=!!r[5];return(a<=23&&i<=59&&n<=59||23==a&&59==i&&60==n)&&(!e||s)}let b=/t|\s/i,_=/\/|:/,v=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,w=/[^\\]\\Z/;function E(e,t,r="2019-09",a=c(t),s=!0,o=null,l="#",u="#",d=Object.create(null)){let h;if(!0===t)return{valid:!0,errors:[]};if(!1===t)return{valid:!1,errors:[{instanceLocation:l,keyword:"false",keywordLocation:l,error:"False boolean schema."}]};let p=typeof e;switch(p){case"boolean":case"number":case"string":h=p;break;case"object":h=null===e?"null":Array.isArray(e)?"array":"object";break;default:throw Error(`Instances of "${p}" type are not supported.`)}let{$ref:f,$recursiveRef:g,$recursiveAnchor:y,type:b,const:_,enum:v,required:w,not:O,anyOf:$,allOf:I,oneOf:S,if:k,then:A,else:T,format:x,properties:j,patternProperties:P,additionalProperties:R,unevaluatedProperties:N,minProperties:C,maxProperties:L,propertyNames:M,dependentRequired:z,dependentSchemas:U,dependencies:D,prefixItems:F,items:B,additionalItems:H,unevaluatedItems:G,contains:Z,minContains:q,maxContains:J,minItems:Y,maxItems:W,uniqueItems:V,minimum:X,maximum:K,exclusiveMinimum:Q,exclusiveMaximum:ee,multipleOf:et,minLength:er,maxLength:ea,pattern:ei,__absolute_ref__:en,__absolute_recursive_ref__:es}=t,eo=[];if(!0===y&&null===o&&(o=t),"#"===g){let i=null===o?a[es]:o,n=`${u}/$recursiveRef`,c=E(e,null===o?t:o,r,a,s,i,l,n,d);c.valid||eo.push({instanceLocation:l,keyword:"$recursiveRef",keywordLocation:n,error:"A subschema had errors."},...c.errors)}if(void 0!==f){let t=a[en||f];if(void 0===t){let e=`Unresolved $ref "${f}".`;throw en&&en!==f&&(e+=`  Absolute URI "${en}".`),Error(e+=`
Known schemas:
- ${Object.keys(a).join("\n- ")}`)}let i=`${u}/$ref`,n=E(e,t,r,a,s,o,l,i,d);if(n.valid||eo.push({instanceLocation:l,keyword:"$ref",keywordLocation:i,error:"A subschema had errors."},...n.errors),"4"===r||"7"===r)return{valid:0===eo.length,errors:eo}}if(Array.isArray(b)){let t=b.length,r=!1;for(let a=0;a<t;a++)if(h===b[a]||"integer"===b[a]&&"number"===h&&e%1==0&&e==e){r=!0;break}r||eo.push({instanceLocation:l,keyword:"type",keywordLocation:`${u}/type`,error:`Instance type "${h}" is invalid. Expected "${b.join('", "')}".`})}else"integer"===b?("number"!==h||e%1||e!=e)&&eo.push({instanceLocation:l,keyword:"type",keywordLocation:`${u}/type`,error:`Instance type "${h}" is invalid. Expected "${b}".`}):void 0!==b&&h!==b&&eo.push({instanceLocation:l,keyword:"type",keywordLocation:`${u}/type`,error:`Instance type "${h}" is invalid. Expected "${b}".`});if(void 0!==_&&("object"===h||"array"===h?i(e,_)||eo.push({instanceLocation:l,keyword:"const",keywordLocation:`${u}/const`,error:`Instance does not match ${JSON.stringify(_)}.`}):e!==_&&eo.push({instanceLocation:l,keyword:"const",keywordLocation:`${u}/const`,error:`Instance does not match ${JSON.stringify(_)}.`})),void 0!==v&&("object"===h||"array"===h?v.some(t=>i(e,t))||eo.push({instanceLocation:l,keyword:"enum",keywordLocation:`${u}/enum`,error:`Instance does not match any of ${JSON.stringify(v)}.`}):v.some(t=>e===t)||eo.push({instanceLocation:l,keyword:"enum",keywordLocation:`${u}/enum`,error:`Instance does not match any of ${JSON.stringify(v)}.`})),void 0!==O){let t=`${u}/not`;E(e,O,r,a,s,o,l,t).valid&&eo.push({instanceLocation:l,keyword:"not",keywordLocation:t,error:'Instance matched "not" schema.'})}let el=[];if(void 0!==$){let t=`${u}/anyOf`,i=eo.length,n=!1;for(let i=0;i<$.length;i++){let u=$[i],c=Object.create(d),h=E(e,u,r,a,s,!0===y?o:null,l,`${t}/${i}`,c);eo.push(...h.errors),n=n||h.valid,h.valid&&el.push(c)}n?eo.length=i:eo.splice(i,0,{instanceLocation:l,keyword:"anyOf",keywordLocation:t,error:"Instance does not match any subschemas."})}if(void 0!==I){let t=`${u}/allOf`,i=eo.length,n=!0;for(let i=0;i<I.length;i++){let u=I[i],c=Object.create(d),h=E(e,u,r,a,s,!0===y?o:null,l,`${t}/${i}`,c);eo.push(...h.errors),n=n&&h.valid,h.valid&&el.push(c)}n?eo.length=i:eo.splice(i,0,{instanceLocation:l,keyword:"allOf",keywordLocation:t,error:"Instance does not match every subschema."})}if(void 0!==S){let t=`${u}/oneOf`,i=eo.length,n=S.filter((i,n)=>{let u=Object.create(d),c=E(e,i,r,a,s,!0===y?o:null,l,`${t}/${n}`,u);return eo.push(...c.errors),c.valid&&el.push(u),c.valid}).length;1===n?eo.length=i:eo.splice(i,0,{instanceLocation:l,keyword:"oneOf",keywordLocation:t,error:`Instance does not match exactly one subschema (${n} matches).`})}if(("object"===h||"array"===h)&&Object.assign(d,...el),void 0!==k){let t=`${u}/if`;if(E(e,k,r,a,s,o,l,t,d).valid){if(void 0!==A){let i=E(e,A,r,a,s,o,l,`${u}/then`,d);i.valid||eo.push({instanceLocation:l,keyword:"if",keywordLocation:t,error:'Instance does not match "then" schema.'},...i.errors)}}else if(void 0!==T){let i=E(e,T,r,a,s,o,l,`${u}/else`,d);i.valid||eo.push({instanceLocation:l,keyword:"if",keywordLocation:t,error:'Instance does not match "else" schema.'},...i.errors)}}if("object"===h){if(void 0!==w)for(let t of w)t in e||eo.push({instanceLocation:l,keyword:"required",keywordLocation:`${u}/required`,error:`Instance does not have required property "${t}".`});let t=Object.keys(e);if(void 0!==C&&t.length<C&&eo.push({instanceLocation:l,keyword:"minProperties",keywordLocation:`${u}/minProperties`,error:`Instance does not have at least ${C} properties.`}),void 0!==L&&t.length>L&&eo.push({instanceLocation:l,keyword:"maxProperties",keywordLocation:`${u}/maxProperties`,error:`Instance does not have at least ${L} properties.`}),void 0!==M){let t=`${u}/propertyNames`;for(let i in e){let e=`${l}/${n(i)}`,u=E(i,M,r,a,s,o,e,t);u.valid||eo.push({instanceLocation:l,keyword:"propertyNames",keywordLocation:t,error:`Property name "${i}" does not match schema.`},...u.errors)}}if(void 0!==z){let t=`${u}/dependantRequired`;for(let r in z)if(r in e)for(let a of z[r])a in e||eo.push({instanceLocation:l,keyword:"dependentRequired",keywordLocation:t,error:`Instance has "${r}" but does not have "${a}".`})}if(void 0!==U)for(let t in U){let i=`${u}/dependentSchemas`;if(t in e){let u=E(e,U[t],r,a,s,o,l,`${i}/${n(t)}`,d);u.valid||eo.push({instanceLocation:l,keyword:"dependentSchemas",keywordLocation:i,error:`Instance has "${t}" but does not match dependant schema.`},...u.errors)}}if(void 0!==D){let t=`${u}/dependencies`;for(let i in D)if(i in e){let u=D[i];if(Array.isArray(u))for(let r of u)r in e||eo.push({instanceLocation:l,keyword:"dependencies",keywordLocation:t,error:`Instance has "${i}" but does not have "${r}".`});else{let c=E(e,u,r,a,s,o,l,`${t}/${n(i)}`);c.valid||eo.push({instanceLocation:l,keyword:"dependencies",keywordLocation:t,error:`Instance has "${i}" but does not match dependant schema.`},...c.errors)}}}let i=Object.create(null),c=!1;if(void 0!==j){let t=`${u}/properties`;for(let u in j){if(!(u in e))continue;let h=`${l}/${n(u)}`,p=E(e[u],j[u],r,a,s,o,h,`${t}/${n(u)}`);if(p.valid)d[u]=i[u]=!0;else if(c=s,eo.push({instanceLocation:l,keyword:"properties",keywordLocation:t,error:`Property "${u}" does not match schema.`},...p.errors),c)break}}if(!c&&void 0!==P){let t=`${u}/patternProperties`;for(let u in P){let h=RegExp(u,"u"),p=P[u];for(let f in e){if(!h.test(f))continue;let m=`${l}/${n(f)}`,g=E(e[f],p,r,a,s,o,m,`${t}/${n(u)}`);g.valid?d[f]=i[f]=!0:(c=s,eo.push({instanceLocation:l,keyword:"patternProperties",keywordLocation:t,error:`Property "${f}" matches pattern "${u}" but does not match associated schema.`},...g.errors))}}}if(c||void 0===R){if(!c&&void 0!==N){let t=`${u}/unevaluatedProperties`;for(let i in e)if(!d[i]){let u=`${l}/${n(i)}`,c=E(e[i],N,r,a,s,o,u,t);c.valid?d[i]=!0:eo.push({instanceLocation:l,keyword:"unevaluatedProperties",keywordLocation:t,error:`Property "${i}" does not match unevaluated properties schema.`},...c.errors)}}}else{let t=`${u}/additionalProperties`;for(let u in e){if(i[u])continue;let h=`${l}/${n(u)}`,p=E(e[u],R,r,a,s,o,h,t);p.valid?d[u]=!0:(c=s,eo.push({instanceLocation:l,keyword:"additionalProperties",keywordLocation:t,error:`Property "${u}" does not match additional properties schema.`},...p.errors))}}}else if("array"===h){void 0!==W&&e.length>W&&eo.push({instanceLocation:l,keyword:"maxItems",keywordLocation:`${u}/maxItems`,error:`Array has too many items (${e.length} > ${W}).`}),void 0!==Y&&e.length<Y&&eo.push({instanceLocation:l,keyword:"minItems",keywordLocation:`${u}/minItems`,error:`Array has too few items (${e.length} < ${Y}).`});let t=e.length,n=0,c=!1;if(void 0!==F){let i=`${u}/prefixItems`,h=Math.min(F.length,t);for(;n<h;n++){let t=E(e[n],F[n],r,a,s,o,`${l}/${n}`,`${i}/${n}`);if(d[n]=!0,!t.valid&&(c=s,eo.push({instanceLocation:l,keyword:"prefixItems",keywordLocation:i,error:"Items did not match schema."},...t.errors),c))break}}if(void 0!==B){let i=`${u}/items`;if(Array.isArray(B)){let u=Math.min(B.length,t);for(;n<u;n++){let t=E(e[n],B[n],r,a,s,o,`${l}/${n}`,`${i}/${n}`);if(d[n]=!0,!t.valid&&(c=s,eo.push({instanceLocation:l,keyword:"items",keywordLocation:i,error:"Items did not match schema."},...t.errors),c))break}}else for(;n<t;n++){let t=E(e[n],B,r,a,s,o,`${l}/${n}`,i);if(d[n]=!0,!t.valid&&(c=s,eo.push({instanceLocation:l,keyword:"items",keywordLocation:i,error:"Items did not match schema."},...t.errors),c))break}if(!c&&void 0!==H){let i=`${u}/additionalItems`;for(;n<t;n++){let t=E(e[n],H,r,a,s,o,`${l}/${n}`,i);d[n]=!0,t.valid||(c=s,eo.push({instanceLocation:l,keyword:"additionalItems",keywordLocation:i,error:"Items did not match additional items schema."},...t.errors))}}}if(void 0!==Z)if(0===t&&void 0===q)eo.push({instanceLocation:l,keyword:"contains",keywordLocation:`${u}/contains`,error:"Array is empty. It must contain at least one item matching the schema."});else if(void 0!==q&&t<q)eo.push({instanceLocation:l,keyword:"minContains",keywordLocation:`${u}/minContains`,error:`Array has less items (${t}) than minContains (${q}).`});else{let i=`${u}/contains`,n=eo.length,c=0;for(let n=0;n<t;n++){let t=E(e[n],Z,r,a,s,o,`${l}/${n}`,i);t.valid?(d[n]=!0,c++):eo.push(...t.errors)}c>=(q||0)&&(eo.length=n),void 0===q&&void 0===J&&0===c?eo.splice(n,0,{instanceLocation:l,keyword:"contains",keywordLocation:i,error:"Array does not contain item matching schema."}):void 0!==q&&c<q?eo.push({instanceLocation:l,keyword:"minContains",keywordLocation:`${u}/minContains`,error:`Array must contain at least ${q} items matching schema. Only ${c} items were found.`}):void 0!==J&&c>J&&eo.push({instanceLocation:l,keyword:"maxContains",keywordLocation:`${u}/maxContains`,error:`Array may contain at most ${J} items matching schema. ${c} items were found.`})}if(!c&&void 0!==G){let i=`${u}/unevaluatedItems`;for(;n<t;n++){if(d[n])continue;let t=E(e[n],G,r,a,s,o,`${l}/${n}`,i);d[n]=!0,t.valid||eo.push({instanceLocation:l,keyword:"unevaluatedItems",keywordLocation:i,error:"Items did not match unevaluated items schema."},...t.errors)}}if(V)for(let r=0;r<t;r++){let a=e[r],n="object"==typeof a&&null!==a;for(let s=0;s<t;s++){if(r===s)continue;let t=e[s],o="object"==typeof t&&null!==t;(a===t||n&&o&&i(a,t))&&(eo.push({instanceLocation:l,keyword:"uniqueItems",keywordLocation:`${u}/uniqueItems`,error:`Duplicate items at indexes ${r} and ${s}.`}),r=Number.MAX_SAFE_INTEGER,s=Number.MAX_SAFE_INTEGER)}}}else if("number"===h){if("4"===r?(void 0!==X&&(!0===Q&&e<=X||e<X)&&eo.push({instanceLocation:l,keyword:"minimum",keywordLocation:`${u}/minimum`,error:`${e} is less than ${Q?"or equal to ":""} ${X}.`}),void 0!==K&&(!0===ee&&e>=K||e>K)&&eo.push({instanceLocation:l,keyword:"maximum",keywordLocation:`${u}/maximum`,error:`${e} is greater than ${ee?"or equal to ":""} ${K}.`})):(void 0!==X&&e<X&&eo.push({instanceLocation:l,keyword:"minimum",keywordLocation:`${u}/minimum`,error:`${e} is less than ${X}.`}),void 0!==K&&e>K&&eo.push({instanceLocation:l,keyword:"maximum",keywordLocation:`${u}/maximum`,error:`${e} is greater than ${K}.`}),void 0!==Q&&e<=Q&&eo.push({instanceLocation:l,keyword:"exclusiveMinimum",keywordLocation:`${u}/exclusiveMinimum`,error:`${e} is less than ${Q}.`}),void 0!==ee&&e>=ee&&eo.push({instanceLocation:l,keyword:"exclusiveMaximum",keywordLocation:`${u}/exclusiveMaximum`,error:`${e} is greater than or equal to ${ee}.`})),void 0!==et){let t=e%et;Math.abs(0-t)>=11920929e-14&&Math.abs(et-t)>=11920929e-14&&eo.push({instanceLocation:l,keyword:"multipleOf",keywordLocation:`${u}/multipleOf`,error:`${e} is not a multiple of ${et}.`})}}else if("string"===h){let t=void 0===er&&void 0===ea?0:function(e){let t,r=0,a=e.length,i=0;for(;i<a;)r++,(t=e.charCodeAt(i++))>=55296&&t<=56319&&i<a&&(64512&(t=e.charCodeAt(i)))==56320&&i++;return r}(e);void 0!==er&&t<er&&eo.push({instanceLocation:l,keyword:"minLength",keywordLocation:`${u}/minLength`,error:`String is too short (${t} < ${er}).`}),void 0!==ea&&t>ea&&eo.push({instanceLocation:l,keyword:"maxLength",keywordLocation:`${u}/maxLength`,error:`String is too long (${t} > ${ea}).`}),void 0===ei||RegExp(ei,"u").test(e)||eo.push({instanceLocation:l,keyword:"pattern",keywordLocation:`${u}/pattern`,error:"String does not match pattern."}),void 0!==x&&m[x]&&!m[x](e)&&eo.push({instanceLocation:l,keyword:"format",keywordLocation:`${u}/format`,error:`String does not match format "${x}".`})}return{valid:0===eo.length,errors:eo}}!function(e){e[e.Flag=1]="Flag",e[e.Basic=2]="Basic",e[e.Detailed=4]="Detailed"}(a||(a={}));class O{schema;draft;shortCircuit;lookup;constructor(e,t="2019-09",r=!0){this.schema=e,this.draft=t,this.shortCircuit=r,this.lookup=c(e)}validate(e){return E(e,this.schema,this.draft,this.lookup,this.shortCircuit)}addSchema(e,t){t&&(e={...e,$id:t}),c(e,this.lookup)}}},17266:(e,t,r)=>{"use strict";r.d(t,{Yx:()=>o,rO:()=>s});var a=r(59438);let i=(...e)=>fetch(...e),n=Symbol.for("ls:fetch_implementation"),s=()=>{let e=globalThis[n];return!!e&&"function"==typeof e&&"Headers"in e&&"Request"in e&&"Response"in e},o=e=>async(...t)=>{if(e||"true"===(0,a.Jz)("DEBUG")){let[e,r]=t;console.log(`→ ${r?.method||"GET"} ${e}`)}let r=await (globalThis[n]??i)(...t);return(e||"true"===(0,a.Jz)("DEBUG"))&&console.log(`← ${r.status} ${r.statusText} ${r.url}`),r}},17950:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t)=>a(e,t,!0)},18929:(e,t)=>{"use strict";t.byteLength=function(e){var t=l(e),r=t[0],a=t[1];return(r+a)*3/4-a},t.toByteArray=function(e){var t,r,n=l(e),s=n[0],o=n[1],u=new i((s+o)*3/4-o),c=0,d=o>0?s-4:s;for(r=0;r<d;r+=4)t=a[e.charCodeAt(r)]<<18|a[e.charCodeAt(r+1)]<<12|a[e.charCodeAt(r+2)]<<6|a[e.charCodeAt(r+3)],u[c++]=t>>16&255,u[c++]=t>>8&255,u[c++]=255&t;return 2===o&&(t=a[e.charCodeAt(r)]<<2|a[e.charCodeAt(r+1)]>>4,u[c++]=255&t),1===o&&(t=a[e.charCodeAt(r)]<<10|a[e.charCodeAt(r+1)]<<4|a[e.charCodeAt(r+2)]>>2,u[c++]=t>>8&255,u[c++]=255&t),u},t.fromByteArray=function(e){for(var t,a=e.length,i=a%3,n=[],s=0,o=a-i;s<o;s+=16383)n.push(function(e,t,a){for(var i,n=[],s=t;s<a;s+=3)i=(e[s]<<16&0xff0000)+(e[s+1]<<8&65280)+(255&e[s+2]),n.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return n.join("")}(e,s,s+16383>o?o:s+16383));return 1===i?n.push(r[(t=e[a-1])>>2]+r[t<<4&63]+"=="):2===i&&n.push(r[(t=(e[a-2]<<8)+e[a-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),n.join("")};for(var r=[],a=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,o=n.length;s<o;++s)r[s]=n[s],a[n.charCodeAt(s)]=s;function l(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var a=r===t?0:4-r%4;return[r,a]}a[45]=62,a[95]=63},20938:(e,t,r)=>{"use strict";let a=r(3706);e.exports=(e,t)=>new a(e,t).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))},21760:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{Validator:()=>C.Dr,deepCompareStrict:()=>C.rA,toJsonSchema:()=>M,validatesOnlyStrings:()=>function e(t){if(!t||"object"!=typeof t||0===Object.keys(t).length||Array.isArray(t))return!1;if("type"in t)return"string"==typeof t.type?"string"===t.type:!!Array.isArray(t.type)&&t.type.every(e=>"string"===e);if("enum"in t)return Array.isArray(t.enum)&&t.enum.length>0&&t.enum.every(e=>"string"==typeof e);if("const"in t)return"string"==typeof t.const;if("allOf"in t&&Array.isArray(t.allOf))return t.allOf.some(t=>e(t));if("anyOf"in t&&Array.isArray(t.anyOf)||"oneOf"in t&&Array.isArray(t.oneOf)){let r="anyOf"in t?t.anyOf:t.oneOf;return r.length>0&&r.every(t=>e(t))}if("not"in t)return!1;if("$ref"in t&&"string"==typeof t.$ref){let r=t.$ref,a=(0,C.DS)(t);if(a[r])return e(a[r])}return!1}});var i=r(31661);let n=Symbol("Let zodToJsonSchema decide on which parser to use"),s={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref"},o=e=>"string"==typeof e?{...s,name:e}:{...s,...e},l=e=>{let t=o(e),r=void 0!==t.name?[...t.basePath,t.definitionPath,t.name]:t.basePath;return{...t,currentPath:r,propertyPath:void 0,seen:new Map(Object.entries(t.definitions).map(([e,r])=>[r._def,{def:r._def,path:[...t.basePath,t.definitionPath,e],jsonSchema:void 0}]))}};var u=r(45697);function c(e,t,r,a){a?.errorMessages&&r&&(e.errorMessage={...e.errorMessage,[t]:r})}function d(e,t,r,a,i){e[t]=r,c(e,t,a,i)}function h(e,t){return x(e.type._def,t)}let p=(e,t)=>x(e.innerType._def,t),f=(e,t)=>{let r={type:"integer",format:"unix-time"};if("openApi3"===t.target)return r;for(let a of e.checks)switch(a.kind){case"min":d(r,"minimum",a.value,a.message,t);break;case"max":d(r,"maximum",a.value,a.message,t)}return r},m=e=>(!("type"in e)||"string"!==e.type)&&"allOf"in e,g={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(void 0===a&&(a=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),a),ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function y(e,t){let r={type:"string"};if(e.checks)for(let a of e.checks)switch(a.kind){case"min":d(r,"minLength","number"==typeof r.minLength?Math.max(r.minLength,a.value):a.value,a.message,t);break;case"max":d(r,"maxLength","number"==typeof r.maxLength?Math.min(r.maxLength,a.value):a.value,a.message,t);break;case"email":switch(t.emailStrategy){case"format:email":v(r,"email",a.message,t);break;case"format:idn-email":v(r,"idn-email",a.message,t);break;case"pattern:zod":w(r,g.email,a.message,t)}break;case"url":v(r,"uri",a.message,t);break;case"uuid":v(r,"uuid",a.message,t);break;case"regex":w(r,a.regex,a.message,t);break;case"cuid":w(r,g.cuid,a.message,t);break;case"cuid2":w(r,g.cuid2,a.message,t);break;case"startsWith":w(r,RegExp(`^${b(a.value,t)}`),a.message,t);break;case"endsWith":w(r,RegExp(`${b(a.value,t)}$`),a.message,t);break;case"datetime":v(r,"date-time",a.message,t);break;case"date":v(r,"date",a.message,t);break;case"time":v(r,"time",a.message,t);break;case"duration":v(r,"duration",a.message,t);break;case"length":d(r,"minLength","number"==typeof r.minLength?Math.max(r.minLength,a.value):a.value,a.message,t),d(r,"maxLength","number"==typeof r.maxLength?Math.min(r.maxLength,a.value):a.value,a.message,t);break;case"includes":w(r,RegExp(b(a.value,t)),a.message,t);break;case"ip":"v6"!==a.version&&v(r,"ipv4",a.message,t),"v4"!==a.version&&v(r,"ipv6",a.message,t);break;case"base64url":w(r,g.base64url,a.message,t);break;case"jwt":w(r,g.jwt,a.message,t);break;case"cidr":"v6"!==a.version&&w(r,g.ipv4Cidr,a.message,t),"v4"!==a.version&&w(r,g.ipv6Cidr,a.message,t);break;case"emoji":w(r,g.emoji(),a.message,t);break;case"ulid":w(r,g.ulid,a.message,t);break;case"base64":switch(t.base64Strategy){case"format:binary":v(r,"binary",a.message,t);break;case"contentEncoding:base64":d(r,"contentEncoding","base64",a.message,t);break;case"pattern:zod":w(r,g.base64,a.message,t)}break;case"nanoid":w(r,g.nanoid,a.message,t)}return r}function b(e,t){return"escape"===t.patternStrategy?function(e){let t="";for(let r=0;r<e.length;r++)_.has(e[r])||(t+="\\"),t+=e[r];return t}(e):e}let _=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function v(e,t,r,a){e.format||e.anyOf?.some(e=>e.format)?(e.anyOf||(e.anyOf=[]),e.format&&(e.anyOf.push({format:e.format,...e.errorMessage&&a.errorMessages&&{errorMessage:{format:e.errorMessage.format}}}),delete e.format,e.errorMessage&&(delete e.errorMessage.format,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.anyOf.push({format:t,...r&&a.errorMessages&&{errorMessage:{format:r}}})):d(e,"format",t,r,a)}function w(e,t,r,a){e.pattern||e.allOf?.some(e=>e.pattern)?(e.allOf||(e.allOf=[]),e.pattern&&(e.allOf.push({pattern:e.pattern,...e.errorMessage&&a.errorMessages&&{errorMessage:{pattern:e.errorMessage.pattern}}}),delete e.pattern,e.errorMessage&&(delete e.errorMessage.pattern,0===Object.keys(e.errorMessage).length&&delete e.errorMessage)),e.allOf.push({pattern:E(t,a),...r&&a.errorMessages&&{errorMessage:{pattern:r}}})):d(e,"pattern",E(t,a),r,a)}function E(e,t){if(!t.applyRegexFlags||!e.flags)return e.source;let r={i:e.flags.includes("i"),m:e.flags.includes("m"),s:e.flags.includes("s")},a=r.i?e.source.toLowerCase():e.source,i="",n=!1,s=!1,o=!1;for(let e=0;e<a.length;e++){if(n){i+=a[e],n=!1;continue}if(r.i){if(s){if(a[e].match(/[a-z]/)){o?(i+=a[e],i+=`${a[e-2]}-${a[e]}`.toUpperCase(),o=!1):"-"===a[e+1]&&a[e+2]?.match(/[a-z]/)?(i+=a[e],o=!0):i+=`${a[e]}${a[e].toUpperCase()}`;continue}}else if(a[e].match(/[a-z]/)){i+=`[${a[e]}${a[e].toUpperCase()}]`;continue}}if(r.m){if("^"===a[e]){i+=`(^|(?<=[\r
]))`;continue}else if("$"===a[e]){i+=`($|(?=[\r
]))`;continue}}if(r.s&&"."===a[e]){i+=s?`${a[e]}\r
`:`[${a[e]}\r
]`;continue}i+=a[e],"\\"===a[e]?n=!0:s&&"]"===a[e]?s=!1:s||"["!==a[e]||(s=!0)}try{new RegExp(i)}catch{return console.warn(`Could not convert regex pattern at ${t.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),e.source}return i}function O(e,t){if("openAi"===t.target&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),"openApi3"===t.target&&e.keyType?._def.typeName===u.kY.ZodEnum)return{type:"object",required:e.keyType._def.values,properties:e.keyType._def.values.reduce((r,a)=>({...r,[a]:x(e.valueType._def,{...t,currentPath:[...t.currentPath,"properties",a]})??{}}),{}),additionalProperties:t.rejectedAdditionalProperties};let r={type:"object",additionalProperties:x(e.valueType._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]})??t.allowedAdditionalProperties};if("openApi3"===t.target)return r;if(e.keyType?._def.typeName===u.kY.ZodString&&e.keyType._def.checks?.length){let{type:a,...i}=y(e.keyType._def,t);return{...r,propertyNames:i}}if(e.keyType?._def.typeName===u.kY.ZodEnum)return{...r,propertyNames:{enum:e.keyType._def.values}};if(e.keyType?._def.typeName===u.kY.ZodBranded&&e.keyType._def.type._def.typeName===u.kY.ZodString&&e.keyType._def.type._def.checks?.length){let{type:a,...i}=h(e.keyType._def,t);return{...r,propertyNames:i}}return r}let $={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"},I=(e,t)=>{let r=(e.options instanceof Map?Array.from(e.options.values()):e.options).map((e,r)=>x(e._def,{...t,currentPath:[...t.currentPath,"anyOf",`${r}`]})).filter(e=>!!e&&(!t.strictUnions||"object"==typeof e&&Object.keys(e).length>0));return r.length?{anyOf:r}:void 0},S=(e,t)=>{if(t.currentPath.toString()===t.propertyPath?.toString())return x(e.innerType._def,t);let r=x(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","1"]});return r?{anyOf:[{not:{}},r]}:{}},k=(e,t)=>{if("input"===t.pipeStrategy)return x(e.in._def,t);if("output"===t.pipeStrategy)return x(e.out._def,t);let r=x(e.in._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),a=x(e.out._def,{...t,currentPath:[...t.currentPath,"allOf",r?"1":"0"]});return{allOf:[r,a].filter(e=>void 0!==e)}},A=(e,t)=>x(e.innerType._def,t),T=(e,t,r)=>{switch(t){case u.kY.ZodString:return y(e,r);case u.kY.ZodNumber:return function(e,t){let r={type:"number"};if(!e.checks)return r;for(let a of e.checks)switch(a.kind){case"int":r.type="integer",c(r,"type",a.message,t);break;case"min":"jsonSchema7"===t.target?a.inclusive?d(r,"minimum",a.value,a.message,t):d(r,"exclusiveMinimum",a.value,a.message,t):(a.inclusive||(r.exclusiveMinimum=!0),d(r,"minimum",a.value,a.message,t));break;case"max":"jsonSchema7"===t.target?a.inclusive?d(r,"maximum",a.value,a.message,t):d(r,"exclusiveMaximum",a.value,a.message,t):(a.inclusive||(r.exclusiveMaximum=!0),d(r,"maximum",a.value,a.message,t));break;case"multipleOf":d(r,"multipleOf",a.value,a.message,t)}return r}(e,r);case u.kY.ZodObject:return function(e,t){let r="openAi"===t.target,a={type:"object",properties:{}},i=[],n=e.shape();for(let e in n){let s=n[e];if(void 0===s||void 0===s._def)continue;let o=function(e){try{return e.isOptional()}catch{return!0}}(s);o&&r&&(s instanceof u.Ii&&(s=s._def.innerType),s.isNullable()||(s=s.nullable()),o=!1);let l=x(s._def,{...t,currentPath:[...t.currentPath,"properties",e],propertyPath:[...t.currentPath,"properties",e]});void 0!==l&&(a.properties[e]=l,o||i.push(e))}i.length&&(a.required=i);let s=function(e,t){if("ZodNever"!==e.catchall._def.typeName)return x(e.catchall._def,{...t,currentPath:[...t.currentPath,"additionalProperties"]});switch(e.unknownKeys){case"passthrough":return t.allowedAdditionalProperties;case"strict":return t.rejectedAdditionalProperties;case"strip":return"strict"===t.removeAdditionalStrategy?t.allowedAdditionalProperties:t.rejectedAdditionalProperties}}(e,t);return void 0!==s&&(a.additionalProperties=s),a}(e,r);case u.kY.ZodBigInt:return function(e,t){let r={type:"integer",format:"int64"};if(!e.checks)return r;for(let a of e.checks)switch(a.kind){case"min":"jsonSchema7"===t.target?a.inclusive?d(r,"minimum",a.value,a.message,t):d(r,"exclusiveMinimum",a.value,a.message,t):(a.inclusive||(r.exclusiveMinimum=!0),d(r,"minimum",a.value,a.message,t));break;case"max":"jsonSchema7"===t.target?a.inclusive?d(r,"maximum",a.value,a.message,t):d(r,"exclusiveMaximum",a.value,a.message,t):(a.inclusive||(r.exclusiveMaximum=!0),d(r,"maximum",a.value,a.message,t));break;case"multipleOf":d(r,"multipleOf",a.value,a.message,t)}return r}(e,r);case u.kY.ZodBoolean:return{type:"boolean"};case u.kY.ZodDate:return function e(t,r,a){let i=a??r.dateStrategy;if(Array.isArray(i))return{anyOf:i.map((a,i)=>e(t,r,a))};switch(i){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return f(t,r)}}(e,r);case u.kY.ZodUndefined:return{not:{}};case u.kY.ZodNull:return"openApi3"===r.target?{enum:["null"],nullable:!0}:{type:"null"};case u.kY.ZodArray:return function(e,t){let r={type:"array"};return e.type?._def&&e.type?._def?.typeName!==u.kY.ZodAny&&(r.items=x(e.type._def,{...t,currentPath:[...t.currentPath,"items"]})),e.minLength&&d(r,"minItems",e.minLength.value,e.minLength.message,t),e.maxLength&&d(r,"maxItems",e.maxLength.value,e.maxLength.message,t),e.exactLength&&(d(r,"minItems",e.exactLength.value,e.exactLength.message,t),d(r,"maxItems",e.exactLength.value,e.exactLength.message,t)),r}(e,r);case u.kY.ZodUnion:case u.kY.ZodDiscriminatedUnion:return function(e,t){if("openApi3"===t.target)return I(e,t);let r=e.options instanceof Map?Array.from(e.options.values()):e.options;if(r.every(e=>e._def.typeName in $&&(!e._def.checks||!e._def.checks.length))){let e=r.reduce((e,t)=>{let r=$[t._def.typeName];return r&&!e.includes(r)?[...e,r]:e},[]);return{type:e.length>1?e:e[0]}}if(r.every(e=>"ZodLiteral"===e._def.typeName&&!e.description)){let e=r.reduce((e,t)=>{let r=typeof t._def.value;switch(r){case"string":case"number":case"boolean":return[...e,r];case"bigint":return[...e,"integer"];case"object":if(null===t._def.value)return[...e,"null"];default:return e}},[]);if(e.length===r.length){let t=e.filter((e,t,r)=>r.indexOf(e)===t);return{type:t.length>1?t:t[0],enum:r.reduce((e,t)=>e.includes(t._def.value)?e:[...e,t._def.value],[])}}}else if(r.every(e=>"ZodEnum"===e._def.typeName))return{type:"string",enum:r.reduce((e,t)=>[...e,...t._def.values.filter(t=>!e.includes(t))],[])};return I(e,t)}(e,r);case u.kY.ZodIntersection:return function(e,t){let r=[x(e.left._def,{...t,currentPath:[...t.currentPath,"allOf","0"]}),x(e.right._def,{...t,currentPath:[...t.currentPath,"allOf","1"]})].filter(e=>!!e),a="jsonSchema2019-09"===t.target?{unevaluatedProperties:!1}:void 0,i=[];return r.forEach(e=>{if(m(e))i.push(...e.allOf),void 0===e.unevaluatedProperties&&(a=void 0);else{let t=e;if("additionalProperties"in e&&!1===e.additionalProperties){let{additionalProperties:r,...a}=e;t=a}else a=void 0;i.push(t)}}),i.length?{allOf:i,...a}:void 0}(e,r);case u.kY.ZodTuple:return function(e,t){return e.rest?{type:"array",minItems:e.items.length,items:e.items.map((e,r)=>x(e._def,{...t,currentPath:[...t.currentPath,"items",`${r}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[]),additionalItems:x(e.rest._def,{...t,currentPath:[...t.currentPath,"additionalItems"]})}:{type:"array",minItems:e.items.length,maxItems:e.items.length,items:e.items.map((e,r)=>x(e._def,{...t,currentPath:[...t.currentPath,"items",`${r}`]})).reduce((e,t)=>void 0===t?e:[...e,t],[])}}(e,r);case u.kY.ZodRecord:return O(e,r);case u.kY.ZodLiteral:return function(e,t){let r=typeof e.value;return"bigint"!==r&&"number"!==r&&"boolean"!==r&&"string"!==r?{type:Array.isArray(e.value)?"array":"object"}:"openApi3"===t.target?{type:"bigint"===r?"integer":r,enum:[e.value]}:{type:"bigint"===r?"integer":r,const:e.value}}(e,r);case u.kY.ZodEnum:return{type:"string",enum:Array.from(e.values)};case u.kY.ZodNativeEnum:return function(e){let t=e.values,r=Object.keys(e.values).filter(e=>"number"!=typeof t[t[e]]).map(e=>t[e]),a=Array.from(new Set(r.map(e=>typeof e)));return{type:1===a.length?"string"===a[0]?"string":"number":["string","number"],enum:r}}(e);case u.kY.ZodNullable:return function(e,t){if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(e.innerType._def.typeName)&&(!e.innerType._def.checks||!e.innerType._def.checks.length))return"openApi3"===t.target?{type:$[e.innerType._def.typeName],nullable:!0}:{type:[$[e.innerType._def.typeName],"null"]};if("openApi3"===t.target){let r=x(e.innerType._def,{...t,currentPath:[...t.currentPath]});return r&&"$ref"in r?{allOf:[r],nullable:!0}:r&&{...r,nullable:!0}}let r=x(e.innerType._def,{...t,currentPath:[...t.currentPath,"anyOf","0"]});return r&&{anyOf:[r,{type:"null"}]}}(e,r);case u.kY.ZodOptional:return S(e,r);case u.kY.ZodMap:return function(e,t){return"record"===t.mapStrategy?O(e,t):{type:"array",maxItems:125,items:{type:"array",items:[x(e.keyType._def,{...t,currentPath:[...t.currentPath,"items","items","0"]})||{},x(e.valueType._def,{...t,currentPath:[...t.currentPath,"items","items","1"]})||{}],minItems:2,maxItems:2}}}(e,r);case u.kY.ZodSet:return function(e,t){let r={type:"array",uniqueItems:!0,items:x(e.valueType._def,{...t,currentPath:[...t.currentPath,"items"]})};return e.minSize&&d(r,"minItems",e.minSize.value,e.minSize.message,t),e.maxSize&&d(r,"maxItems",e.maxSize.value,e.maxSize.message,t),r}(e,r);case u.kY.ZodLazy:return()=>e.getter()._def;case u.kY.ZodPromise:return x(e.type._def,r);case u.kY.ZodNaN:case u.kY.ZodNever:return{not:{}};case u.kY.ZodEffects:return function(e,t){return"input"===t.effectStrategy?x(e.schema._def,t):{}}(e,r);case u.kY.ZodAny:case u.kY.ZodUnknown:return{};case u.kY.ZodDefault:return function(e,t){return{...x(e.innerType._def,t),default:e.defaultValue()}}(e,r);case u.kY.ZodBranded:return h(e,r);case u.kY.ZodReadonly:return A(e,r);case u.kY.ZodCatch:return p(e,r);case u.kY.ZodPipeline:return k(e,r);case u.kY.ZodFunction:case u.kY.ZodVoid:case u.kY.ZodSymbol:default:return}};function x(e,t,r=!1){let a=t.seen.get(e);if(t.override){let i=t.override?.(e,t,a,r);if(i!==n)return i}if(a&&!r){let e=j(a,t);if(void 0!==e)return e}let i={def:e,path:t.currentPath,jsonSchema:void 0};t.seen.set(e,i);let s=T(e,e.typeName,t),o="function"==typeof s?x(s(),t):s;if(o&&R(e,t,o),t.postProcess){let r=t.postProcess(o,e,t);return i.jsonSchema=o,r}return i.jsonSchema=o,o}let j=(e,t)=>{switch(t.$refStrategy){case"root":return{$ref:e.path.join("/")};case"relative":return{$ref:P(t.currentPath,e.path)};case"none":case"seen":if(e.path.length<t.currentPath.length&&e.path.every((e,r)=>t.currentPath[r]===e))return console.warn(`Recursive reference detected at ${t.currentPath.join("/")}! Defaulting to any`),{};return"seen"===t.$refStrategy?{}:void 0}},P=(e,t)=>{let r=0;for(;r<e.length&&r<t.length&&e[r]===t[r];r++);return[(e.length-r).toString(),...t.slice(r)].join("/")},R=(e,t,r)=>(e.description&&(r.description=e.description,t.markdownDescription&&(r.markdownDescription=e.description)),r),N=(e,t)=>{let r=l(t),a="object"==typeof t&&t.definitions?Object.entries(t.definitions).reduce((e,[t,a])=>({...e,[t]:x(a._def,{...r,currentPath:[...r.basePath,r.definitionPath,t]},!0)??{}}),{}):void 0,i="string"==typeof t?t:t?.nameStrategy==="title"?void 0:t?.name,n=x(e._def,void 0===i?r:{...r,currentPath:[...r.basePath,r.definitionPath,i]},!1)??{},s="object"==typeof t&&void 0!==t.name&&"title"===t.nameStrategy?t.name:void 0;void 0!==s&&(n.title=s);let o=void 0===i?a?{...n,[r.definitionPath]:a}:n:{$ref:[..."relative"===r.$refStrategy?[]:r.basePath,r.definitionPath,i].join("/"),[r.definitionPath]:{...a,[i]:n}};return"jsonSchema7"===r.target?o.$schema="http://json-schema.org/draft-07/schema#":("jsonSchema2019-09"===r.target||"openAi"===r.target)&&(o.$schema="https://json-schema.org/draft/2019-09/schema#"),"openAi"===r.target&&("anyOf"in o||"oneOf"in o||"allOf"in o||"type"in o&&Array.isArray(o.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),o};var C=r(17107),L=r(60157);function M(e){if((0,L.Pq)(e)){let t=(0,L.EN)(e);if(!(0,L.gY)(t))return(0,i.blZ)(e);{let e=(0,L.pE)(t,!0);return(0,i.blZ)(e)}}return(0,L.IU)(e)?N(e):e}},22123:(e,t,r)=>{"use strict";r.d(t,{Nx:()=>c});var a=r(91962),i=r(4346),n=r(89706);class s{getStore(){}run(e,t){return t()}enterWith(e){}}let o=new s,l=Symbol.for("lc:child_config");class u{getInstance(){return(0,i.X0)()??o}getRunnableConfig(){let e=this.getInstance();return e.getStore()?.extra?.[l]}runWithConfig(e,t,r){let s,o=n.CallbackManager._configureSync(e?.callbacks,void 0,e?.tags,void 0,e?.metadata),u=this.getInstance(),c=u.getStore(),d=o?.getParentRunId(),h=o?.handlers?.find(e=>e?.name==="langchain_tracer");return h&&d?s=h.getRunTreeWithTracingConfig(d):r||(s=new a.gk({name:"<runnable_lambda>",tracingEnabled:!1})),s&&(s.extra={...s.extra,[l]:e}),void 0!==c&&void 0!==c[i.hr]&&(void 0===s&&(s={}),s[i.hr]=c[i.hr]),u.run(s,t)}initializeGlobalInstance(e){void 0===(0,i.X0)()&&(0,i.pH)(e)}}let c=new u},22893:(e,t,r)=>{"use strict";let a=r(43528);e.exports=(e,t,r)=>a(e,t,"<",r)},23518:(e,t,r)=>{e.exports=r(55332)},24303:(e,t,r)=>{"use strict";let a=r(64487),i=r(3706);e.exports=(e,t,r)=>{let n=null,s=null,o=null;try{o=new i(t,r)}catch(e){return null}return e.forEach(e=>{o.test(e)&&(!n||1===s.compare(e))&&(s=new a(n=e,r))}),n}},24800:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t,r)=>{let i=new a(e,r),n=new a(t,r);return i.compare(n)||i.compareBuild(n)}},25706:e=>{"use strict";e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},26515:(e,t,r)=>{"use strict";let{MAX_SAFE_COMPONENT_LENGTH:a,MAX_SAFE_BUILD_LENGTH:i,MAX_LENGTH:n}=r(32397),s=r(38267),o=(t=e.exports={}).re=[],l=t.safeRe=[],u=t.src=[],c=t.safeSrc=[],d=t.t={},h=0,p="[a-zA-Z0-9-]",f=[["\\s",1],["\\d",n],[p,i]],m=e=>{for(let[t,r]of f)e=e.split(`${t}*`).join(`${t}{0,${r}}`).split(`${t}+`).join(`${t}{1,${r}}`);return e},g=(e,t,r)=>{let a=m(t),i=h++;s(e,i,t),d[e]=i,u[i]=t,c[i]=a,o[i]=new RegExp(t,r?"g":void 0),l[i]=new RegExp(a,r?"g":void 0)};g("NUMERICIDENTIFIER","0|[1-9]\\d*"),g("NUMERICIDENTIFIERLOOSE","\\d+"),g("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${p}*`),g("MAINVERSION",`(${u[d.NUMERICIDENTIFIER]})\\.(${u[d.NUMERICIDENTIFIER]})\\.(${u[d.NUMERICIDENTIFIER]})`),g("MAINVERSIONLOOSE",`(${u[d.NUMERICIDENTIFIERLOOSE]})\\.(${u[d.NUMERICIDENTIFIERLOOSE]})\\.(${u[d.NUMERICIDENTIFIERLOOSE]})`),g("PRERELEASEIDENTIFIER",`(?:${u[d.NONNUMERICIDENTIFIER]}|${u[d.NUMERICIDENTIFIER]})`),g("PRERELEASEIDENTIFIERLOOSE",`(?:${u[d.NONNUMERICIDENTIFIER]}|${u[d.NUMERICIDENTIFIERLOOSE]})`),g("PRERELEASE",`(?:-(${u[d.PRERELEASEIDENTIFIER]}(?:\\.${u[d.PRERELEASEIDENTIFIER]})*))`),g("PRERELEASELOOSE",`(?:-?(${u[d.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${u[d.PRERELEASEIDENTIFIERLOOSE]})*))`),g("BUILDIDENTIFIER",`${p}+`),g("BUILD",`(?:\\+(${u[d.BUILDIDENTIFIER]}(?:\\.${u[d.BUILDIDENTIFIER]})*))`),g("FULLPLAIN",`v?${u[d.MAINVERSION]}${u[d.PRERELEASE]}?${u[d.BUILD]}?`),g("FULL",`^${u[d.FULLPLAIN]}$`),g("LOOSEPLAIN",`[v=\\s]*${u[d.MAINVERSIONLOOSE]}${u[d.PRERELEASELOOSE]}?${u[d.BUILD]}?`),g("LOOSE",`^${u[d.LOOSEPLAIN]}$`),g("GTLT","((?:<|>)?=?)"),g("XRANGEIDENTIFIERLOOSE",`${u[d.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),g("XRANGEIDENTIFIER",`${u[d.NUMERICIDENTIFIER]}|x|X|\\*`),g("XRANGEPLAIN",`[v=\\s]*(${u[d.XRANGEIDENTIFIER]})(?:\\.(${u[d.XRANGEIDENTIFIER]})(?:\\.(${u[d.XRANGEIDENTIFIER]})(?:${u[d.PRERELEASE]})?${u[d.BUILD]}?)?)?`),g("XRANGEPLAINLOOSE",`[v=\\s]*(${u[d.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[d.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[d.XRANGEIDENTIFIERLOOSE]})(?:${u[d.PRERELEASELOOSE]})?${u[d.BUILD]}?)?)?`),g("XRANGE",`^${u[d.GTLT]}\\s*${u[d.XRANGEPLAIN]}$`),g("XRANGELOOSE",`^${u[d.GTLT]}\\s*${u[d.XRANGEPLAINLOOSE]}$`),g("COERCEPLAIN",`(^|[^\\d])(\\d{1,${a}})(?:\\.(\\d{1,${a}}))?(?:\\.(\\d{1,${a}}))?`),g("COERCE",`${u[d.COERCEPLAIN]}(?:$|[^\\d])`),g("COERCEFULL",u[d.COERCEPLAIN]+`(?:${u[d.PRERELEASE]})?`+`(?:${u[d.BUILD]})?`+"(?:$|[^\\d])"),g("COERCERTL",u[d.COERCE],!0),g("COERCERTLFULL",u[d.COERCEFULL],!0),g("LONETILDE","(?:~>?)"),g("TILDETRIM",`(\\s*)${u[d.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",g("TILDE",`^${u[d.LONETILDE]}${u[d.XRANGEPLAIN]}$`),g("TILDELOOSE",`^${u[d.LONETILDE]}${u[d.XRANGEPLAINLOOSE]}$`),g("LONECARET","(?:\\^)"),g("CARETTRIM",`(\\s*)${u[d.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",g("CARET",`^${u[d.LONECARET]}${u[d.XRANGEPLAIN]}$`),g("CARETLOOSE",`^${u[d.LONECARET]}${u[d.XRANGEPLAINLOOSE]}$`),g("COMPARATORLOOSE",`^${u[d.GTLT]}\\s*(${u[d.LOOSEPLAIN]})$|^$`),g("COMPARATOR",`^${u[d.GTLT]}\\s*(${u[d.FULLPLAIN]})$|^$`),g("COMPARATORTRIM",`(\\s*)${u[d.GTLT]}\\s*(${u[d.LOOSEPLAIN]}|${u[d.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",g("HYPHENRANGE",`^\\s*(${u[d.XRANGEPLAIN]})\\s+-\\s+(${u[d.XRANGEPLAIN]})\\s*$`),g("HYPHENRANGELOOSE",`^\\s*(${u[d.XRANGEPLAINLOOSE]})\\s+-\\s+(${u[d.XRANGEPLAINLOOSE]})\\s*$`),g("STAR","(<|>)?=?\\s*\\*"),g("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),g("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},27290:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>0!==a(e,t,r)},27935:(e,t,r)=>{"use strict";r.d(t,{DY:()=>d,SV:()=>o,ZI:()=>u,kJ:()=>s,p_:()=>n,tn:()=>c});var a=r(89706),i=r(22123);let n=25;async function s(e){return a.CallbackManager._configureSync(e?.callbacks,void 0,e?.tags,void 0,e?.metadata)}function o(...e){let t={};for(let r of e.filter(e=>!!e))for(let e of Object.keys(r))if("metadata"===e)t[e]={...t[e],...r[e]};else if("tags"===e){let a=t[e]??[];t[e]=[...new Set(a.concat(r[e]??[]))]}else if("configurable"===e)t[e]={...t[e],...r[e]};else if("timeout"===e)void 0===t.timeout?t.timeout=r.timeout:void 0!==r.timeout&&(t.timeout=Math.min(t.timeout,r.timeout));else if("signal"===e)void 0===t.signal?t.signal=r.signal:void 0!==r.signal&&("any"in AbortSignal?t.signal=AbortSignal.any([t.signal,r.signal]):t.signal=r.signal);else if("callbacks"===e){let e=t.callbacks,i=r.callbacks;if(Array.isArray(i))if(e)if(Array.isArray(e))t.callbacks=e.concat(i);else{let r=e.copy();for(let e of i)r.addHandler((0,a.ensureHandler)(e),!0);t.callbacks=r}else t.callbacks=i;else if(i)if(e)if(Array.isArray(e)){let r=i.copy();for(let t of e)r.addHandler((0,a.ensureHandler)(t),!0);t.callbacks=r}else t.callbacks=new a.CallbackManager(i._parentRunId,{handlers:e.handlers.concat(i.handlers),inheritableHandlers:e.inheritableHandlers.concat(i.inheritableHandlers),tags:Array.from(new Set(e.tags.concat(i.tags))),inheritableTags:Array.from(new Set(e.inheritableTags.concat(i.inheritableTags))),metadata:{...e.metadata,...i.metadata}});else t.callbacks=i}else t[e]=r[e]??t[e];return t}let l=new Set(["string","number","boolean"]);function u(e){let t=i.Nx.getRunnableConfig(),r={tags:[],metadata:{},recursionLimit:25,runId:void 0};if(t){let{runId:e,runName:a,...i}=t;r=Object.entries(i).reduce((e,[t,r])=>(void 0!==r&&(e[t]=r),e),r)}if(e&&(r=Object.entries(e).reduce((e,[t,r])=>(void 0!==r&&(e[t]=r),e),r)),r?.configurable)for(let e of Object.keys(r.configurable))l.has(typeof r.configurable[e])&&!r.metadata?.[e]&&(r.metadata||(r.metadata={}),r.metadata[e]=r.configurable[e]);if(void 0!==r.timeout){if(r.timeout<=0)throw Error("Timeout must be a positive number");let e=AbortSignal.timeout(r.timeout);void 0!==r.signal?"any"in AbortSignal&&(r.signal=AbortSignal.any([r.signal,e])):r.signal=e,delete r.timeout}return r}function c(e={},{callbacks:t,maxConcurrency:r,recursionLimit:a,runName:i,configurable:n,runId:s}={}){let o=u(e);return void 0!==t&&(delete o.runName,o.callbacks=t),void 0!==a&&(o.recursionLimit=a),void 0!==r&&(o.maxConcurrency=r),void 0!==i&&(o.runName=i),void 0!==n&&(o.configurable={...o.configurable,...n}),void 0!==s&&delete o.runId,o}function d(e){return e?{configurable:e.configurable,recursionLimit:e.recursionLimit,callbacks:e.callbacks,tags:e.tags,metadata:e.metadata,maxConcurrency:e.maxConcurrency,timeout:e.timeout,signal:e.signal}:void 0}},28279:(e,t,r)=>{"use strict";r.d(t,{XU:()=>n,bU:()=>o,cM:()=>i,kY:()=>s});var a=r(72892);class i extends a.XQ{static lc_name(){return"ChatMessage"}static _chatMessageClass(){return i}constructor(e,t){"string"==typeof e&&(e={content:e,role:t}),super(e),Object.defineProperty(this,"role",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.role=e.role}_getType(){return"generic"}static isInstance(e){return"generic"===e._getType()}get _printableFields(){return{...super._printableFields,role:this.role}}}class n extends a.gj{static lc_name(){return"ChatMessageChunk"}constructor(e,t){"string"==typeof e&&(e={content:e,role:t}),super(e),Object.defineProperty(this,"role",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.role=e.role}_getType(){return"generic"}concat(e){return new n({content:(0,a._I)(this.content,e.content),additional_kwargs:(0,a.ns)(this.additional_kwargs,e.additional_kwargs),response_metadata:(0,a.ns)(this.response_metadata,e.response_metadata),role:this.role,id:this.id??e.id})}get _printableFields(){return{...super._printableFields,role:this.role}}}function s(e){return"generic"===e._getType()}function o(e){return"generic"===e._getType()}},28506:(e,t,r)=>{"use strict";r.r(t),r.d(t,{LogStreamCallbackHandler:()=>h,RunLog:()=>l,RunLogPatch:()=>o,isLogStreamHandler:()=>u});var a=r(91405),i=r(65164),n=r(72180),s=r(16275);class o{constructor(e){Object.defineProperty(this,"ops",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.ops=e.ops??[]}concat(e){let t=this.ops.concat(e.ops),r=(0,a.X6)({},t);return new l({ops:t,state:r[r.length-1].newDocument})}}class l extends o{constructor(e){super(e),Object.defineProperty(this,"state",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.state=e.state}concat(e){let t=this.ops.concat(e.ops),r=(0,a.X6)(this.state,e.ops);return new l({ops:t,state:r[r.length-1].newDocument})}static fromRunLogPatch(e){let t=(0,a.X6)({},e.ops);return new l({ops:e.ops,state:t[t.length-1].newDocument})}}let u=e=>"log_stream_tracer"===e.name;async function c(e,t){if("original"===t)throw Error("Do not assign inputs with original schema drop the key for now. When inputs are added to streamLog they should be added with standardized schema for streaming events.");let{inputs:r}=e;return["retriever","llm","prompt"].includes(e.run_type)?r:1!==Object.keys(r).length||r?.input!==""?r.input:void 0}async function d(e,t){let{outputs:r}=e;return"original"===t||["retriever","llm","prompt"].includes(e.run_type)?r:void 0!==r&&1===Object.keys(r).length&&r?.output!==void 0?r.output:r}class h extends i.BaseTracer{constructor(e){super({_awaitHandler:!0,...e}),Object.defineProperty(this,"autoClose",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_schemaFormat",{enumerable:!0,configurable:!0,writable:!0,value:"original"}),Object.defineProperty(this,"rootId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"keyMapByRunId",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"counterMapByRunName",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"transformStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"writer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"receiveStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"log_stream_tracer"}),Object.defineProperty(this,"lc_prefer_streaming",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.autoClose=e?.autoClose??!0,this.includeNames=e?.includeNames,this.includeTypes=e?.includeTypes,this.includeTags=e?.includeTags,this.excludeNames=e?.excludeNames,this.excludeTypes=e?.excludeTypes,this.excludeTags=e?.excludeTags,this._schemaFormat=e?._schemaFormat??this._schemaFormat,this.transformStream=new TransformStream,this.writer=this.transformStream.writable.getWriter(),this.receiveStream=n.IterableReadableStream.fromReadableStream(this.transformStream.readable)}[Symbol.asyncIterator](){return this.receiveStream}async persistRun(e){}_includeRun(e){if(e.id===this.rootId)return!1;let t=e.tags??[],r=void 0===this.includeNames&&void 0===this.includeTags&&void 0===this.includeTypes;return void 0!==this.includeNames&&(r=r||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(r=r||this.includeTypes.includes(e.run_type)),void 0!==this.includeTags&&(r=r||void 0!==t.find(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(r=r&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(r=r&&!this.excludeTypes.includes(e.run_type)),void 0!==this.excludeTags&&(r=r&&t.every(e=>!this.excludeTags?.includes(e))),r}async *tapOutputIterable(e,t){for await(let r of t){if(e!==this.rootId){let t=this.keyMapByRunId[e];t&&await this.writer.write(new o({ops:[{op:"add",path:`/logs/${t}/streamed_output/-`,value:r}]}))}yield r}}async onRunCreate(e){if(void 0===this.rootId&&(this.rootId=e.id,await this.writer.write(new o({ops:[{op:"replace",path:"",value:{id:e.id,name:e.name,type:e.run_type,streamed_output:[],final_output:void 0,logs:{}}}]}))),!this._includeRun(e))return;void 0===this.counterMapByRunName[e.name]&&(this.counterMapByRunName[e.name]=0),this.counterMapByRunName[e.name]+=1;let t=this.counterMapByRunName[e.name];this.keyMapByRunId[e.id]=1===t?e.name:`${e.name}:${t}`;let r={id:e.id,name:e.name,type:e.run_type,tags:e.tags??[],metadata:e.extra?.metadata??{},start_time:new Date(e.start_time).toISOString(),streamed_output:[],streamed_output_str:[],final_output:void 0,end_time:void 0};"streaming_events"===this._schemaFormat&&(r.inputs=await c(e,this._schemaFormat)),await this.writer.write(new o({ops:[{op:"add",path:`/logs/${this.keyMapByRunId[e.id]}`,value:r}]}))}async onRunUpdate(e){try{let t=this.keyMapByRunId[e.id];if(void 0===t)return;let r=[];"streaming_events"===this._schemaFormat&&r.push({op:"replace",path:`/logs/${t}/inputs`,value:await c(e,this._schemaFormat)}),r.push({op:"add",path:`/logs/${t}/final_output`,value:await d(e,this._schemaFormat)}),void 0!==e.end_time&&r.push({op:"add",path:`/logs/${t}/end_time`,value:new Date(e.end_time).toISOString()});let a=new o({ops:r});await this.writer.write(a)}finally{if(e.id===this.rootId){let t=new o({ops:[{op:"replace",path:"/final_output",value:await d(e,this._schemaFormat)}]});await this.writer.write(t),this.autoClose&&await this.writer.close()}}}async onLLMNewToken(e,t,r){let a,i=this.keyMapByRunId[e.id];if(void 0===i)return;if(void 0!==e.inputs.messages){var n;a=void 0!==(n=r?.chunk)&&void 0!==n.message?r?.chunk:new s.H({id:`run-${e.id}`,content:t})}else a=t;let l=new o({ops:[{op:"add",path:`/logs/${i}/streamed_output_str/-`,value:t},{op:"add",path:`/logs/${i}/streamed_output/-`,value:a}]});await this.writer.write(l)}}},28584:(e,t,r)=>{"use strict";let a=r(26515),i=r(32397),n=r(64487),s=r(78668),o=r(58361),l=r(35444),u=r(73051),c=r(90726),d=r(93419),h=r(42467),p=r(40999),f=r(78172),m=r(7110),g=r(33877),y=r(86605),b=r(17950),_=r(24800),v=r(31904),w=r(8536),E=r(42699),O=r(40720),$=r(73438),I=r(27290),S=r(44156),k=r(60301),A=r(84450),T=r(44449),x=r(14239),j=r(3706),P=r(42679),R=r(20938),N=r(43441),C=r(24303),L=r(36686),M=r(31385),z=r(43528),U=r(43900),D=r(22893),F=r(71505);e.exports={parse:o,valid:l,clean:u,inc:c,diff:d,major:h,minor:p,patch:f,prerelease:m,compare:g,rcompare:y,compareLoose:b,compareBuild:_,sort:v,rsort:w,gt:E,lt:O,eq:$,neq:I,gte:S,lte:k,cmp:A,coerce:T,Comparator:x,Range:j,satisfies:P,toComparators:R,maxSatisfying:N,minSatisfying:C,minVersion:L,validRange:M,outside:z,gtr:U,ltr:D,intersects:F,simplifyRange:r(77860),subset:r(11337),SemVer:n,re:a.re,src:a.src,tokens:a.t,SEMVER_SPEC_VERSION:i.SEMVER_SPEC_VERSION,RELEASE_TYPES:i.RELEASE_TYPES,compareIdentifiers:s.compareIdentifiers,rcompareIdentifiers:s.rcompareIdentifiers}},28604:(e,t,r)=>{"use strict";async function a(e,t){let r;return void 0===t?e:Promise.race([e.catch(e=>{if(!t?.aborted)throw e}),new Promise((e,a)=>{r=()=>{a(Error("Aborted"))},t.addEventListener("abort",r),t.aborted&&a(Error("Aborted"))})]).finally(()=>t.removeEventListener("abort",r))}r.d(t,{o:()=>a})},28895:(e,t,r)=>{"use strict";r.d(t,{P:()=>u,dr:()=>s,fK:()=>i,p1:()=>o,uf:()=>n,wk:()=>l});var a=r(72892);function i(e){return null!=e&&"object"==typeof e&&"lc_direct_tool_output"in e&&!0===e.lc_direct_tool_output}class n extends a.XQ{static lc_name(){return"ToolMessage"}get lc_aliases(){return{tool_call_id:"tool_call_id"}}constructor(e,t,r){"string"==typeof e&&(e={content:e,name:r,tool_call_id:t}),super(e),Object.defineProperty(this,"lc_direct_tool_output",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"status",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tool_call_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"artifact",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.tool_call_id=e.tool_call_id,this.artifact=e.artifact,this.status=e.status}_getType(){return"tool"}static isInstance(e){return"tool"===e._getType()}get _printableFields(){return{...super._printableFields,tool_call_id:this.tool_call_id,artifact:this.artifact}}}class s extends a.gj{constructor(e){super(e),Object.defineProperty(this,"tool_call_id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"status",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"artifact",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.tool_call_id=e.tool_call_id,this.artifact=e.artifact,this.status=e.status}static lc_name(){return"ToolMessageChunk"}_getType(){return"tool"}concat(e){return new s({content:(0,a._I)(this.content,e.content),additional_kwargs:(0,a.ns)(this.additional_kwargs,e.additional_kwargs),response_metadata:(0,a.ns)(this.response_metadata,e.response_metadata),artifact:(0,a.F7)(this.artifact,e.artifact),tool_call_id:this.tool_call_id,id:this.id??e.id,status:(0,a.Iv)(this.status,e.status)})}get _printableFields(){return{...super._printableFields,tool_call_id:this.tool_call_id,artifact:this.artifact}}}function o(e){let t=[],r=[];for(let a of e)if(!a.function)continue;else{let e=a.function.name;try{let r=JSON.parse(a.function.arguments),i={name:e||"",args:r||{},id:a.id};t.push(i)}catch(t){r.push({name:e,args:a.function.arguments,id:a.id,error:"Malformed args."})}}return[t,r]}function l(e){return"tool"===e._getType()}function u(e){return"tool"===e._getType()}},30413:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{awaitAllCallbacks:()=>l,consumeCallback:()=>o});var i=r(71719),n=r(4346),s=r(35638);async function o(e,t){if(!0===t){let t=(0,n.X0)();void 0!==t?await t.run(void 0,async()=>e()):await e()}else void 0===a&&(a=new(0,i.default)({autoStart:!0,concurrency:1})),a.add(async()=>{let t=(0,n.X0)();void 0!==t?await t.run(void 0,async()=>e()):await e()})}async function l(){let e=(0,s.h)();await Promise.allSettled([void 0!==a?a.onIdle():Promise.resolve(),e.awaitPendingTraceBatches()])}},30663:(e,t,r)=>{"use strict";r.d(t,{Kj:()=>a.Kj,Ls:()=>s,gk:()=>i.gk,q7:()=>n.q});var a=r(65381),i=r(11457);r(17266);var n=r(8212);let s="0.3.33"},31385:(e,t,r)=>{"use strict";let a=r(3706);e.exports=(e,t)=>{try{return new a(e,t).range||"*"}catch(e){return null}}},31661:(e,t,r)=>{"use strict";r.d(t,{Umr:()=>ep,igI:()=>ef,GPR:()=>eh,G8g:()=>eE,emL:()=>ew,o8B:()=>C,fd:()=>ev,qgA:()=>es,EJS:()=>eo,blZ:()=>e$,ZSL:()=>a});var a={};function i(e,t,r){function a(r,a){var i;for(let n in Object.defineProperty(r,"_zod",{value:r._zod??{},enumerable:!1}),(i=r._zod).traits??(i.traits=new Set),r._zod.traits.add(e),t(r,a),s.prototype)n in r||Object.defineProperty(r,n,{value:s.prototype[n].bind(r)});r._zod.constr=s,r._zod.def=a}let i=r?.Parent??Object;class n extends i{}function s(e){var t;let i=r?.Parent?new n:this;for(let r of(a(i,e),(t=i._zod).deferred??(t.deferred=[]),i._zod.deferred))r();return i}return Object.defineProperty(n,"name",{value:e}),Object.defineProperty(s,"init",{value:a}),Object.defineProperty(s,Symbol.hasInstance,{value:t=>!!r?.Parent&&t instanceof r.Parent||t?._zod?.traits?.has(e)}),Object.defineProperty(s,"name",{value:e}),s}r.r(a),r.d(a,{BIGINT_FORMAT_RANGES:()=>F,Class:()=>er,NUMBER_FORMAT_RANGES:()=>D,aborted:()=>Y,allowsEval:()=>A,assert:()=>h,assertEqual:()=>l,assertIs:()=>c,assertNever:()=>d,assertNotEqual:()=>u,assignProp:()=>w,cached:()=>g,captureStackTrace:()=>S,cleanEnum:()=>et,cleanRegex:()=>b,clone:()=>C,createTransparentProxy:()=>M,defineLazy:()=>v,esc:()=>I,escapeRegex:()=>N,extend:()=>G,finalizeIssue:()=>X,floatSafeRemainder:()=>_,getElementAtPath:()=>E,getEnumValues:()=>p,getLengthableOrigin:()=>Q,getParsedType:()=>j,getSizableOrigin:()=>K,isObject:()=>k,isPlainObject:()=>T,issue:()=>ee,joinValues:()=>f,jsonStringifyReplacer:()=>m,merge:()=>Z,normalizeParams:()=>L,nullish:()=>y,numKeys:()=>x,omit:()=>H,optionalKeys:()=>U,partial:()=>q,pick:()=>B,prefixIssues:()=>W,primitiveTypes:()=>R,promiseAllObject:()=>O,propertyKeyTypes:()=>P,randomString:()=>$,required:()=>J,stringifyPrimitive:()=>z,unwrapMessage:()=>V}),Symbol("zod_brand");class n extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let s={};function o(e){return e&&Object.assign(s,e),s}function l(e){return e}function u(e){return e}function c(e){}function d(e){throw Error()}function h(e){}function p(e){let t=Object.values(e).filter(e=>"number"==typeof e);return Object.entries(e).filter(([e,r])=>-1===t.indexOf(+e)).map(([e,t])=>t)}function f(e,t="|"){return e.map(e=>z(e)).join(t)}function m(e,t){return"bigint"==typeof t?t.toString():t}function g(e){return{get value(){{let t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function y(e){return null==e}function b(e){let t=+!!e.startsWith("^"),r=e.endsWith("$")?e.length-1:e.length;return e.slice(t,r)}function _(e,t){let r=(e.toString().split(".")[1]||"").length,a=(t.toString().split(".")[1]||"").length,i=r>a?r:a;return Number.parseInt(e.toFixed(i).replace(".",""))%Number.parseInt(t.toFixed(i).replace(".",""))/10**i}function v(e,t,r){Object.defineProperty(e,t,{get(){{let a=r();return e[t]=a,a}},set(r){Object.defineProperty(e,t,{value:r})},configurable:!0})}function w(e,t,r){Object.defineProperty(e,t,{value:r,writable:!0,enumerable:!0,configurable:!0})}function E(e,t){return t?t.reduce((e,t)=>e?.[t],e):e}function O(e){let t=Object.keys(e);return Promise.all(t.map(t=>e[t])).then(e=>{let r={};for(let a=0;a<t.length;a++)r[t[a]]=e[a];return r})}function $(e=10){let t="abcdefghijklmnopqrstuvwxyz",r="";for(let a=0;a<e;a++)r+=t[Math.floor(Math.random()*t.length)];return r}function I(e){return JSON.stringify(e)}let S=Error.captureStackTrace?Error.captureStackTrace:(...e)=>{};function k(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}let A=g(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(e){return!1}});function T(e){if(!1===k(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!1!==k(r)&&!1!==Object.prototype.hasOwnProperty.call(r,"isPrototypeOf")}function x(e){let t=0;for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t++;return t}let j=e=>{let t=typeof e;switch(t){case"undefined":return"undefined";case"string":return"string";case"number":return Number.isNaN(e)?"nan":"number";case"boolean":return"boolean";case"function":return"function";case"bigint":return"bigint";case"symbol":return"symbol";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return"promise";if("undefined"!=typeof Map&&e instanceof Map)return"map";if("undefined"!=typeof Set&&e instanceof Set)return"set";if("undefined"!=typeof Date&&e instanceof Date)return"date";if("undefined"!=typeof File&&e instanceof File)return"file";return"object";default:throw Error(`Unknown data type: ${t}`)}},P=new Set(["string","number","symbol"]),R=new Set(["string","number","bigint","boolean","symbol","undefined"]);function N(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function C(e,t,r){let a=new e._zod.constr(t??e._zod.def);return(!t||r?.parent)&&(a._zod.parent=e),a}function L(e){if(!e)return{};if("string"==typeof e)return{error:()=>e};if(e?.message!==void 0){if(e?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");e.error=e.message}return(delete e.message,"string"==typeof e.error)?{...e,error:()=>e.error}:e}function M(e){let t;return new Proxy({},{get:(r,a,i)=>(t??(t=e()),Reflect.get(t,a,i)),set:(r,a,i,n)=>(t??(t=e()),Reflect.set(t,a,i,n)),has:(r,a)=>(t??(t=e()),Reflect.has(t,a)),deleteProperty:(r,a)=>(t??(t=e()),Reflect.deleteProperty(t,a)),ownKeys:r=>(t??(t=e()),Reflect.ownKeys(t)),getOwnPropertyDescriptor:(r,a)=>(t??(t=e()),Reflect.getOwnPropertyDescriptor(t,a)),defineProperty:(r,a,i)=>(t??(t=e()),Reflect.defineProperty(t,a,i))})}function z(e){return"bigint"==typeof e?e.toString()+"n":"string"==typeof e?`"${e}"`:`${e}`}function U(e){return Object.keys(e).filter(t=>"optional"===e[t]._zod.optin&&"optional"===e[t]._zod.optout)}let D={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-0x80000000,0x7fffffff],uint32:[0,0xffffffff],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]},F={int64:[BigInt("-9223372036854775808"),BigInt("9223372036854775807")],uint64:[BigInt(0),BigInt("18446744073709551615")]};function B(e,t){let r={},a=e._zod.def;for(let e in t){if(!(e in a.shape))throw Error(`Unrecognized key: "${e}"`);t[e]&&(r[e]=a.shape[e])}return C(e,{...e._zod.def,shape:r,checks:[]})}function H(e,t){let r={...e._zod.def.shape},a=e._zod.def;for(let e in t){if(!(e in a.shape))throw Error(`Unrecognized key: "${e}"`);t[e]&&delete r[e]}return C(e,{...e._zod.def,shape:r,checks:[]})}function G(e,t){let r={...e._zod.def,get shape(){let r={...e._zod.def.shape,...t};return w(this,"shape",r),r},checks:[]};return C(e,r)}function Z(e,t){return C(e,{...e._zod.def,get shape(){let r={...e._zod.def.shape,...t._zod.def.shape};return w(this,"shape",r),r},catchall:t._zod.def.catchall,checks:[]})}function q(e,t,r){let a=t._zod.def.shape,i={...a};if(r)for(let t in r){if(!(t in a))throw Error(`Unrecognized key: "${t}"`);r[t]&&(i[t]=e?new e({type:"optional",innerType:a[t]}):a[t])}else for(let t in a)i[t]=e?new e({type:"optional",innerType:a[t]}):a[t];return C(t,{...t._zod.def,shape:i,checks:[]})}function J(e,t,r){let a=t._zod.def.shape,i={...a};if(r)for(let t in r){if(!(t in i))throw Error(`Unrecognized key: "${t}"`);r[t]&&(i[t]=new e({type:"nonoptional",innerType:a[t]}))}else for(let t in a)i[t]=new e({type:"nonoptional",innerType:a[t]});return C(t,{...t._zod.def,shape:i,checks:[]})}function Y(e,t=0){for(let r=t;r<e.issues.length;r++)if(!0!==e.issues[r].continue)return!0;return!1}function W(e,t){return t.map(t=>(t.path??(t.path=[]),t.path.unshift(e),t))}function V(e){return"string"==typeof e?e:e?.message}function X(e,t,r){let a={...e,path:e.path??[]};return e.message||(a.message=V(e.inst?._zod.def?.error?.(e))??V(t?.error?.(e))??V(r.customError?.(e))??V(r.localeError?.(e))??"Invalid input"),delete a.inst,delete a.continue,t?.reportInput||delete a.input,a}function K(e){return e instanceof Set?"set":e instanceof Map?"map":e instanceof File?"file":"unknown"}function Q(e){return Array.isArray(e)?"array":"string"==typeof e?"string":"unknown"}function ee(...e){let[t,r,a]=e;return"string"==typeof t?{message:t,code:"custom",input:r,inst:a}:{...t}}function et(e){return Object.entries(e).filter(([e,t])=>Number.isNaN(Number.parseInt(e,10))).map(e=>e[1])}class er{constructor(...e){}}let ea=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),Object.defineProperty(e,"message",{get:()=>JSON.stringify(t,m,2),enumerable:!0})},ei=i("$ZodError",ea),en=i("$ZodError",ea,{Parent:Error}),es=(e,t,r,a)=>{let i=r?Object.assign(r,{async:!1}):{async:!1},s=e._zod.run({value:t,issues:[]},i);if(s instanceof Promise)throw new n;if(s.issues.length){let e=new(a?.Err??en)(s.issues.map(e=>X(e,i,o())));throw S(e,a?.callee),e}return s.value},eo=async(e,t,r,a)=>{let i=r?Object.assign(r,{async:!0}):{async:!0},n=e._zod.run({value:t,issues:[]},i);if(n instanceof Promise&&(n=await n),n.issues.length){let e=new(a?.Err??en)(n.issues.map(e=>X(e,i,o())));throw S(e,a?.callee),e}return n.value},el=(e,t,r)=>{let a=r?{...r,async:!1}:{async:!1},i=e._zod.run({value:t,issues:[]},a);if(i instanceof Promise)throw new n;return i.issues.length?{success:!1,error:new(en??ei)(i.issues.map(e=>X(e,a,o())))}:{success:!0,data:i.value}},eu=async(e,t,r)=>{let a=r?Object.assign(r,{async:!0}):{async:!0},i=e._zod.run({value:t,issues:[]},a);return i instanceof Promise&&(i=await i),i.issues.length?{success:!1,error:new en(i.issues.map(e=>X(e,a,o())))}:{success:!0,data:i.value}},ec={major:4,minor:0,patch:0},ed=i("$ZodType",(e,t)=>{var r;e??(e={}),v(e._zod,"id",()=>t.type+"_"+$(10)),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=ec;let a=[...e._zod.def.checks??[]];for(let t of(e._zod.traits.has("$ZodCheck")&&a.unshift(e),a))for(let r of t._zod.onattach)r(e);if(0===a.length)(r=e._zod).deferred??(r.deferred=[]),e._zod.deferred?.push(()=>{e._zod.run=e._zod.parse});else{let t=(e,t,r)=>{let a,i=Y(e);for(let s of t){if(s._zod.when){if(!s._zod.when(e))continue}else if(i)continue;let t=e.issues.length,o=s._zod.check(e);if(o instanceof Promise&&r?.async===!1)throw new n;if(a||o instanceof Promise)a=(a??Promise.resolve()).then(async()=>{await o,e.issues.length!==t&&(i||(i=Y(e,t)))});else{if(e.issues.length===t)continue;i||(i=Y(e,t))}}return a?a.then(()=>e):e};e._zod.run=(r,i)=>{let s=e._zod.parse(r,i);if(s instanceof Promise){if(!1===i.async)throw new n;return s.then(e=>t(e,a,i))}return t(s,a,i)}}e["~standard"]={validate:t=>{try{let r=el(e,t);return r.success?{value:r.data}:{issues:r.error?.issues}}catch(r){return eu(e,t).then(e=>e.success?{value:e.data}:{issues:e.error?.issues})}},vendor:"zod",version:1}}),eh=i("$ZodUnknown",(e,t)=>{ed.init(e,t),e._zod.parse=e=>e}),ep=i("$ZodNever",(e,t)=>{ed.init(e,t),e._zod.parse=(t,r)=>(t.issues.push({expected:"never",code:"invalid_type",input:t.value,inst:e}),t)}),ef=i("$ZodOptional",(e,t)=>{ed.init(e,t),e._zod.optin="optional",e._zod.optout="optional",v(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),v(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${b(e.source)})?$`):void 0}),e._zod.parse=(e,r)=>void 0===e.value?e:t.innerType._zod.run(e,r)});function em(e,t,r,a){let i=Math.abs(e),n=i%10,s=i%100;return s>=11&&s<=19?a:1===n?t:n>=2&&n<=4?r:a}let eg=e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t};function ey(e,t,r,a){let i=Math.abs(e),n=i%10,s=i%100;return s>=11&&s<=19?a:1===n?t:n>=2&&n<=4?r:a}let eb=e=>{let t=typeof e;switch(t){case"number":return Number.isNaN(e)?"NaN":"number";case"object":if(Array.isArray(e))return"array";if(null===e)return"null";if(Object.getPrototypeOf(e)!==Object.prototype&&e.constructor)return e.constructor.name}return t};Symbol("ZodOutput"),Symbol("ZodInput");class e_{constructor(){this._map=new WeakMap,this._idmap=new Map}add(e,...t){let r=t[0];if(this._map.set(e,r),r&&"object"==typeof r&&"id"in r){if(this._idmap.has(r.id))throw Error(`ID ${r.id} already exists in the registry`);this._idmap.set(r.id,e)}return this}remove(e){return this._map.delete(e),this}get(e){let t=e._zod.parent;if(t){let r={...this.get(t)??{}};return delete r.id,{...r,...this._map.get(e)}}return this._map.get(e)}has(e){return this._map.has(e)}}let ev=new e_;function ew(e){return new e({type:"unknown"})}function eE(e,t){return new e({type:"never",...L(t)})}class eO{constructor(e){this.counter=0,this.metadataRegistry=e?.metadata??ev,this.target=e?.target??"draft-2020-12",this.unrepresentable=e?.unrepresentable??"throw",this.override=e?.override??(()=>{}),this.io=e?.io??"output",this.seen=new Map}process(e,t={path:[],schemaPath:[]}){var r;let a=e._zod.def,i=this.seen.get(e);if(i)return i.count++,t.schemaPath.includes(e)&&(i.cycle=t.path),i.schema;let n={schema:{},count:1,cycle:void 0};this.seen.set(e,n);let s=e._zod.toJSONSchema?.();if(s)n.schema=s;else{let r={...t,schemaPath:[...t.schemaPath,e],path:t.path},i=e._zod.parent;if(i)n.ref=i,this.process(i,r),this.seen.get(i).isParent=!0;else{let t=n.schema;switch(a.type){case"string":{t.type="string";let{minimum:r,maximum:a,format:i,patterns:s,contentEncoding:o}=e._zod.bag;if("number"==typeof r&&(t.minLength=r),"number"==typeof a&&(t.maxLength=a),i&&(t.format=({guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""})[i]??i,""===t.format&&delete t.format),o&&(t.contentEncoding=o),s&&s.size>0){let e=[...s];1===e.length?t.pattern=e[0].source:e.length>1&&(n.schema.allOf=[...e.map(e=>({..."draft-7"===this.target?{type:"string"}:{},pattern:e.source}))])}break}case"number":{let{minimum:r,maximum:a,format:i,multipleOf:n,exclusiveMaximum:s,exclusiveMinimum:o}=e._zod.bag;"string"==typeof i&&i.includes("int")?t.type="integer":t.type="number","number"==typeof o&&(t.exclusiveMinimum=o),"number"==typeof r&&(t.minimum=r,"number"==typeof o&&(o>=r?delete t.minimum:delete t.exclusiveMinimum)),"number"==typeof s&&(t.exclusiveMaximum=s),"number"==typeof a&&(t.maximum=a,"number"==typeof s&&(s<=a?delete t.maximum:delete t.exclusiveMaximum)),"number"==typeof n&&(t.multipleOf=n);break}case"boolean":case"success":t.type="boolean";break;case"bigint":if("throw"===this.unrepresentable)throw Error("BigInt cannot be represented in JSON Schema");break;case"symbol":if("throw"===this.unrepresentable)throw Error("Symbols cannot be represented in JSON Schema");break;case"undefined":case"null":t.type="null";break;case"any":case"unknown":break;case"never":t.not={};break;case"void":if("throw"===this.unrepresentable)throw Error("Void cannot be represented in JSON Schema");break;case"date":if("throw"===this.unrepresentable)throw Error("Date cannot be represented in JSON Schema");break;case"array":{let{minimum:i,maximum:n}=e._zod.bag;"number"==typeof i&&(t.minItems=i),"number"==typeof n&&(t.maxItems=n),t.type="array",t.items=this.process(a.element,{...r,path:[...r.path,"items"]});break}case"object":{t.type="object",t.properties={};let e=a.shape;for(let a in e)t.properties[a]=this.process(e[a],{...r,path:[...r.path,"properties",a]});let i=new Set([...new Set(Object.keys(e))].filter(e=>{let t=a.shape[e]._zod;return"input"===this.io?void 0===t.optin:void 0===t.optout}));i.size>0&&(t.required=Array.from(i)),a.catchall?._zod.def.type==="never"?t.additionalProperties=!1:a.catchall?a.catchall&&(t.additionalProperties=this.process(a.catchall,{...r,path:[...r.path,"additionalProperties"]})):"output"===this.io&&(t.additionalProperties=!1);break}case"union":t.anyOf=a.options.map((e,t)=>this.process(e,{...r,path:[...r.path,"anyOf",t]}));break;case"intersection":{let e=this.process(a.left,{...r,path:[...r.path,"allOf",0]}),i=this.process(a.right,{...r,path:[...r.path,"allOf",1]}),n=e=>"allOf"in e&&1===Object.keys(e).length;t.allOf=[...n(e)?e.allOf:[e],...n(i)?i.allOf:[i]];break}case"tuple":{t.type="array";let i=a.items.map((e,t)=>this.process(e,{...r,path:[...r.path,"prefixItems",t]}));if("draft-2020-12"===this.target?t.prefixItems=i:t.items=i,a.rest){let e=this.process(a.rest,{...r,path:[...r.path,"items"]});"draft-2020-12"===this.target?t.items=e:t.additionalItems=e}a.rest&&(t.items=this.process(a.rest,{...r,path:[...r.path,"items"]}));let{minimum:n,maximum:s}=e._zod.bag;"number"==typeof n&&(t.minItems=n),"number"==typeof s&&(t.maxItems=s);break}case"record":t.type="object",t.propertyNames=this.process(a.keyType,{...r,path:[...r.path,"propertyNames"]}),t.additionalProperties=this.process(a.valueType,{...r,path:[...r.path,"additionalProperties"]});break;case"map":if("throw"===this.unrepresentable)throw Error("Map cannot be represented in JSON Schema");break;case"set":if("throw"===this.unrepresentable)throw Error("Set cannot be represented in JSON Schema");break;case"enum":{let e=p(a.entries);e.every(e=>"number"==typeof e)&&(t.type="number"),e.every(e=>"string"==typeof e)&&(t.type="string"),t.enum=e;break}case"literal":{let e=[];for(let t of a.values)if(void 0===t){if("throw"===this.unrepresentable)throw Error("Literal `undefined` cannot be represented in JSON Schema")}else if("bigint"==typeof t)if("throw"===this.unrepresentable)throw Error("BigInt literals cannot be represented in JSON Schema");else e.push(Number(t));else e.push(t);if(0===e.length);else if(1===e.length){let r=e[0];t.type=null===r?"null":typeof r,t.const=r}else e.every(e=>"number"==typeof e)&&(t.type="number"),e.every(e=>"string"==typeof e)&&(t.type="string"),e.every(e=>"boolean"==typeof e)&&(t.type="string"),e.every(e=>null===e)&&(t.type="null"),t.enum=e;break}case"file":{let r={type:"string",format:"binary",contentEncoding:"binary"},{minimum:a,maximum:i,mime:n}=e._zod.bag;void 0!==a&&(r.minLength=a),void 0!==i&&(r.maxLength=i),n?1===n.length?(r.contentMediaType=n[0],Object.assign(t,r)):t.anyOf=n.map(e=>({...r,contentMediaType:e})):Object.assign(t,r);break}case"transform":if("throw"===this.unrepresentable)throw Error("Transforms cannot be represented in JSON Schema");break;case"nullable":t.anyOf=[this.process(a.innerType,r),{type:"null"}];break;case"nonoptional":case"promise":case"optional":this.process(a.innerType,r),n.ref=a.innerType;break;case"default":this.process(a.innerType,r),n.ref=a.innerType,t.default=JSON.parse(JSON.stringify(a.defaultValue));break;case"prefault":this.process(a.innerType,r),n.ref=a.innerType,"input"===this.io&&(t._prefault=JSON.parse(JSON.stringify(a.defaultValue)));break;case"catch":{let e;this.process(a.innerType,r),n.ref=a.innerType;try{e=a.catchValue(void 0)}catch{throw Error("Dynamic catch values are not supported in JSON Schema")}t.default=e;break}case"nan":if("throw"===this.unrepresentable)throw Error("NaN cannot be represented in JSON Schema");break;case"template_literal":{let r=e._zod.pattern;if(!r)throw Error("Pattern not found in template literal");t.type="string",t.pattern=r.source;break}case"pipe":{let e="input"===this.io?"transform"===a.in._zod.def.type?a.out:a.in:a.out;this.process(e,r),n.ref=e;break}case"readonly":this.process(a.innerType,r),n.ref=a.innerType,t.readOnly=!0;break;case"lazy":{let t=e._zod.innerType;this.process(t,r),n.ref=t;break}case"custom":if("throw"===this.unrepresentable)throw Error("Custom types cannot be represented in JSON Schema")}}}let o=this.metadataRegistry.get(e);return o&&Object.assign(n.schema,o),"input"===this.io&&function e(t,r){let a=r??{seen:new Set};if(a.seen.has(t))return!1;a.seen.add(t);let i=t._zod.def;switch(i.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":case"custom":case"success":case"catch":return!1;case"array":return e(i.element,a);case"object":for(let t in i.shape)if(e(i.shape[t],a))return!0;return!1;case"union":for(let t of i.options)if(e(t,a))return!0;return!1;case"intersection":return e(i.left,a)||e(i.right,a);case"tuple":for(let t of i.items)if(e(t,a))return!0;if(i.rest&&e(i.rest,a))return!0;return!1;case"record":case"map":return e(i.keyType,a)||e(i.valueType,a);case"set":return e(i.valueType,a);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":case"default":case"prefault":return e(i.innerType,a);case"lazy":return e(i.getter(),a);case"transform":return!0;case"pipe":return e(i.in,a)||e(i.out,a)}throw Error(`Unknown schema type: ${i.type}`)}(e)&&(delete n.schema.examples,delete n.schema.default),"input"===this.io&&n.schema._prefault&&((r=n.schema).default??(r.default=n.schema._prefault)),delete n.schema._prefault,this.seen.get(e).schema}emit(e,t){let r={cycles:t?.cycles??"ref",reused:t?.reused??"inline",external:t?.external??void 0},a=this.seen.get(e);if(!a)throw Error("Unprocessed schema. This is a bug in Zod.");let i=e=>{let t="draft-2020-12"===this.target?"$defs":"definitions";if(r.external){let a=r.external.registry.get(e[0])?.id;if(a)return{ref:r.external.uri(a)};let i=e[1].defId??e[1].schema.id??`schema${this.counter++}`;return e[1].defId=i,{defId:i,ref:`${r.external.uri("__shared")}#/${t}/${i}`}}if(e[1]===a)return{ref:"#"};let i=`#/${t}/`,n=e[1].schema.id??`__schema${this.counter++}`;return{defId:n,ref:i+n}},n=e=>{if(e[1].schema.$ref)return;let t=e[1],{ref:r,defId:a}=i(e);t.def={...t.schema},a&&(t.defId=a);let n=t.schema;for(let e in n)delete n[e];n.$ref=r};for(let t of this.seen.entries()){let a=t[1];if(e===t[0]){n(t);continue}if(r.external){let a=r.external.registry.get(t[0])?.id;if(e!==t[0]&&a){n(t);continue}}if(this.metadataRegistry.get(t[0])?.id){n(t);continue}if(a.cycle){if("throw"===r.cycles)throw Error(`Cycle detected: #/${a.cycle?.join("/")}/<root>

Set the \`cycles\` parameter to \`"ref"\` to resolve cyclical schemas with defs.`);"ref"===r.cycles&&n(t);continue}if(a.count>1&&"ref"===r.reused){n(t);continue}}let s=(e,t)=>{let r=this.seen.get(e),a=r.def??r.schema,i={...a};if(null===r.ref)return;let n=r.ref;if(r.ref=null,n){s(n,t);let e=this.seen.get(n).schema;e.$ref&&"draft-7"===t.target?(a.allOf=a.allOf??[],a.allOf.push(e)):(Object.assign(a,e),Object.assign(a,i))}r.isParent||this.override({zodSchema:e,jsonSchema:a})};for(let e of[...this.seen.entries()].reverse())s(e[0],{target:this.target});let o={};"draft-2020-12"===this.target?o.$schema="https://json-schema.org/draft/2020-12/schema":"draft-7"===this.target?o.$schema="http://json-schema.org/draft-07/schema#":console.warn(`Invalid target: ${this.target}`),Object.assign(o,a.def);let l=r.external?.defs??{};for(let e of this.seen.entries()){let t=e[1];t.def&&t.defId&&(l[t.defId]=t.def)}!r.external&&Object.keys(l).length>0&&("draft-2020-12"===this.target?o.$defs=l:o.definitions=l);try{return JSON.parse(JSON.stringify(o))}catch(e){throw Error("Error converting schema to JSON.")}}}function e$(e,t){if(e instanceof e_){let r=new eO(t),a={};for(let t of e._idmap.entries()){let[e,a]=t;r.process(a)}let i={},n={registry:e,uri:t?.uri||(e=>e),defs:a};for(let a of e._idmap.entries()){let[e,s]=a;i[e]=r.emit(s,{...t,external:n})}return Object.keys(a).length>0&&(i.__shared={["draft-2020-12"===r.target?"$defs":"definitions"]:a}),{schemas:i}}let r=new eO(t);return r.process(e),r.emit(e,t)}},31904:(e,t,r)=>{"use strict";let a=r(24800);e.exports=(e,t)=>e.sort((e,r)=>a(e,r,t))},32397:e=>{"use strict";e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER||0x1fffffffffffff,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},33877:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t,r)=>new a(e,r).compare(new a(t,r))},35444:(e,t,r)=>{"use strict";let a=r(58361);e.exports=(e,t)=>{let r=a(e,t);return r?r.version:null}},35638:(e,t,r)=>{"use strict";let a;r.d(t,{h:()=>s});var i=r(91962),n=r(56301);let s=()=>{if(void 0===a){let e="false"===(0,n.getEnvironmentVariable)("LANGCHAIN_CALLBACKS_BACKGROUND")?{blockOnRootRunFinalization:!0}:{};a=new i.Kj(e)}return a}},36686:(e,t,r)=>{"use strict";let a=r(64487),i=r(3706),n=r(42699);e.exports=(e,t)=>{e=new i(e,t);let r=new a("0.0.0");if(e.test(r)||(r=new a("0.0.0-0"),e.test(r)))return r;r=null;for(let t=0;t<e.set.length;++t){let i=e.set[t],s=null;i.forEach(e=>{let t=new a(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":(!s||n(t,s))&&(s=t);break;case"<":case"<=":break;default:throw Error(`Unexpected operation: ${e.operator}`)}}),s&&(!r||n(r,s))&&(r=s)}return r&&e.test(r)?r:null}},37664:(e,t,r)=>{"use strict";r.d(t,{vR:()=>u,GZ:()=>c});var a=r(11457);class i{getStore(){}run(e,t){return t()}}let n=Symbol.for("ls:tracing_async_local_storage"),s=new i;class o{getInstance(){return globalThis[n]??s}initializeGlobalInstance(e){void 0===globalThis[n]&&(globalThis[n]=e)}}let l=new o;function u(e=!1){let t=l.getInstance().getStore();if(!e&&!(0,a.m5)(t))throw Error("Could not get the current run tree.\n\nPlease make sure you are calling this method within a traceable function and that tracing is enabled.");return t}function c(e){return"function"==typeof e&&"langsmith:traceable"in e}Symbol.for("langsmith:traceable:root")},38022:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ChatGenerationChunk:()=>n,GenerationChunk:()=>i,RUN_KEY:()=>a});let a="__run";class i{constructor(e){Object.defineProperty(this,"text",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"generationInfo",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.text=e.text,this.generationInfo=e.generationInfo}concat(e){return new i({text:this.text+e.text,generationInfo:{...this.generationInfo,...e.generationInfo}})}}class n extends i{constructor(e){super(e),Object.defineProperty(this,"message",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.message=e.message}concat(e){return new n({text:this.text+e.text,generationInfo:{...this.generationInfo,...e.generationInfo},message:this.message.concat(e.message)})}}},38267:e=>{"use strict";e.exports="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{}},40720:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>0>a(e,t,r)},40999:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t)=>new a(e,t).minor},42467:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t)=>new a(e,t).major},42679:(e,t,r)=>{"use strict";let a=r(3706);e.exports=(e,t,r)=>{try{t=new a(t,r)}catch(e){return!1}return t.test(e)}},42699:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>a(e,t,r)>0},43441:(e,t,r)=>{"use strict";let a=r(64487),i=r(3706);e.exports=(e,t,r)=>{let n=null,s=null,o=null;try{o=new i(t,r)}catch(e){return null}return e.forEach(e=>{o.test(e)&&(!n||-1===s.compare(e))&&(s=new a(n=e,r))}),n}},43528:(e,t,r)=>{"use strict";let a=r(64487),i=r(14239),{ANY:n}=i,s=r(3706),o=r(42679),l=r(42699),u=r(40720),c=r(60301),d=r(44156);e.exports=(e,t,r,h)=>{let p,f,m,g,y;switch(e=new a(e,h),t=new s(t,h),r){case">":p=l,f=c,m=u,g=">",y=">=";break;case"<":p=u,f=d,m=l,g="<",y="<=";break;default:throw TypeError('Must provide a hilo val of "<" or ">"')}if(o(e,t,h))return!1;for(let r=0;r<t.set.length;++r){let a=t.set[r],s=null,o=null;if(a.forEach(e=>{e.semver===n&&(e=new i(">=0.0.0")),s=s||e,o=o||e,p(e.semver,s.semver,h)?s=e:m(e.semver,o.semver,h)&&(o=e)}),s.operator===g||s.operator===y||(!o.operator||o.operator===g)&&f(e,o.semver)||o.operator===y&&m(e,o.semver))return!1}return!0}},43900:(e,t,r)=>{"use strict";let a=r(43528);e.exports=(e,t,r)=>a(e,t,">",r)},44156:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>a(e,t,r)>=0},44449:(e,t,r)=>{"use strict";let a=r(64487),i=r(58361),{safeRe:n,t:s}=r(26515);e.exports=(e,t)=>{if(e instanceof a)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let r=null;if((t=t||{}).rtl){let a,i=t.includePrerelease?n[s.COERCERTLFULL]:n[s.COERCERTL];for(;(a=i.exec(e))&&(!r||r.index+r[0].length!==e.length);)r&&a.index+a[0].length===r.index+r[0].length||(r=a),i.lastIndex=a.index+a[1].length+a[2].length;i.lastIndex=-1}else r=e.match(t.includePrerelease?n[s.COERCEFULL]:n[s.COERCE]);if(null===r)return null;let o=r[2],l=r[3]||"0",u=r[4]||"0",c=t.includePrerelease&&r[5]?`-${r[5]}`:"",d=t.includePrerelease&&r[6]?`+${r[6]}`:"";return i(`${o}.${l}.${u}${c}${d}`,t)}},47948:(e,t,r)=>{"use strict";r.d(t,{y:()=>a});class a{constructor(e){Object.defineProperty(this,"pageContent",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.pageContent=void 0!==e.pageContent?e.pageContent.toString():"",this.metadata=e.metadata??{},this.id=e.id}}},48457:(e,t,r)=>{"use strict";function a(e){return!!(e&&"object"==typeof e&&"type"in e&&"tool_call"===e.type)}function i(e){return!!(e&&"object"==typeof e&&"toolCall"in e&&null!=e.toolCall&&"object"==typeof e.toolCall&&"id"in e.toolCall&&"string"==typeof e.toolCall.id)}r.d(t,{Ky:()=>a,hR:()=>i,qe:()=>n});class n extends Error{constructor(e,t){super(e),Object.defineProperty(this,"output",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.output=t}}},49517:(e,t,r)=>{"use strict";function a(e){return"object"==typeof e&&null!==e&&"type"in e&&"string"==typeof e.type&&"source_type"in e&&("url"===e.source_type||"base64"===e.source_type||"text"===e.source_type||"id"===e.source_type)}function i(e){return a(e)&&"url"===e.source_type&&"url"in e&&"string"==typeof e.url}function n(e){return a(e)&&"base64"===e.source_type&&"data"in e&&"string"==typeof e.data}function s(e){return a(e)&&"text"===e.source_type&&"text"in e&&"string"==typeof e.text}function o(e){return a(e)&&"id"===e.source_type&&"id"in e&&"string"==typeof e.id}function l(e){if(a(e)){if("url"===e.source_type)return{type:"image_url",image_url:{url:e.url}};if("base64"===e.source_type){if(!e.mime_type)throw Error("mime_type key is required for base64 data.");let t=e.mime_type;return{type:"image_url",image_url:{url:`data:${t};base64,${e.data}`}}}}throw Error("Unsupported source type. Only 'url' and 'base64' are supported.")}function u(e){let t=e.split(";")[0].split("/");if(2!==t.length)throw Error(`Invalid mime type: "${e}" - does not match type/subtype format.`);let r=t[0].trim(),a=t[1].trim();if(""===r||""===a)throw Error(`Invalid mime type: "${e}" - type or subtype is empty.`);let i={};for(let t of e.split(";").slice(1)){let r=t.split("=");if(2!==r.length)throw Error(`Invalid parameter syntax in mime type: "${e}".`);let a=r[0].trim(),n=r[1].trim();if(""===a)throw Error(`Invalid parameter syntax in mime type: "${e}".`);i[a]=n}return{type:r,subtype:a,parameters:i}}function c({dataUrl:e,asTypedArray:t=!1}){let r=e.match(/^data:(\w+\/\w+);base64,([A-Za-z0-9+/]+=*)$/);if(r)return{mime_type:r[1].toLowerCase(),data:t?Uint8Array.from(atob(r[2]),e=>e.charCodeAt(0)):r[2]}}function d(e,t){if("text"===e.type){if(!t.fromStandardTextBlock)throw Error(`Converter for ${t.providerName} does not implement \`fromStandardTextBlock\` method.`);return t.fromStandardTextBlock(e)}if("image"===e.type){if(!t.fromStandardImageBlock)throw Error(`Converter for ${t.providerName} does not implement \`fromStandardImageBlock\` method.`);return t.fromStandardImageBlock(e)}if("audio"===e.type){if(!t.fromStandardAudioBlock)throw Error(`Converter for ${t.providerName} does not implement \`fromStandardAudioBlock\` method.`);return t.fromStandardAudioBlock(e)}if("file"===e.type){if(!t.fromStandardFileBlock)throw Error(`Converter for ${t.providerName} does not implement \`fromStandardFileBlock\` method.`);return t.fromStandardFileBlock(e)}throw Error(`Unable to convert content block type '${e.type}' to provider-specific format: not recognized.`)}r.d(t,{Ac:()=>s,Ej:()=>u,Fz:()=>a,Q7:()=>c,Vi:()=>l,W:()=>i,cJ:()=>n,oe:()=>o,up:()=>d})},51862:e=>{"use strict";let t=/[\p{Lu}]/u,r=/[\p{Ll}]/u,a=/^[\p{Lu}](?![\p{Lu}])/gu,i=/([\p{Alpha}\p{N}_]|$)/u,n=/[_.\- ]+/,s=RegExp("^"+n.source),o=RegExp(n.source+i.source,"gu"),l=RegExp("\\d+"+i.source,"gu"),u=(e,a,i)=>{let n=!1,s=!1,o=!1;for(let l=0;l<e.length;l++){let u=e[l];n&&t.test(u)?(e=e.slice(0,l)+"-"+e.slice(l),n=!1,o=s,s=!0,l++):s&&o&&r.test(u)?(e=e.slice(0,l-1)+"-"+e.slice(l-1),o=s,s=!1,n=!0):(n=a(u)===u&&i(u)!==u,o=s,s=i(u)===u&&a(u)!==u)}return e},c=(e,t)=>(a.lastIndex=0,e.replace(a,e=>t(e))),d=(e,t)=>(o.lastIndex=0,l.lastIndex=0,e.replace(o,(e,r)=>t(r)).replace(l,e=>t(e))),h=(e,t)=>{if(!("string"==typeof e||Array.isArray(e)))throw TypeError("Expected the input to be `string | string[]`");if(t={pascalCase:!1,preserveConsecutiveUppercase:!1,...t},0===(e=Array.isArray(e)?e.map(e=>e.trim()).filter(e=>e.length).join("-"):e.trim()).length)return"";let r=!1===t.locale?e=>e.toLowerCase():e=>e.toLocaleLowerCase(t.locale),a=!1===t.locale?e=>e.toUpperCase():e=>e.toLocaleUpperCase(t.locale);return 1===e.length?t.pascalCase?a(e):r(e):(e!==r(e)&&(e=u(e,r,a)),e=e.replace(s,""),e=t.preserveConsecutiveUppercase?c(e,r):r(e),t.pascalCase&&(e=a(e.charAt(0))+e.slice(1)),d(e,a))};e.exports=h,e.exports.default=h},52832:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ConsoleCallbackHandler:()=>c});var a=r(63391),i=r(65164);function n(e,t){return`${e.open}${t}${e.close}`}function s(e,t){try{return JSON.stringify(e,null,2)}catch(e){return t}}function o(e){return"string"==typeof e?e.trim():null==e?e:s(e,e.toString())}function l(e){if(!e.end_time)return"";let t=e.end_time-e.start_time;return t<1e3?`${t}ms`:`${(t/1e3).toFixed(2)}s`}let{color:u}=a;class c extends i.BaseTracer{constructor(){super(...arguments),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"console_callback_handler"})}persistRun(e){return Promise.resolve()}getParents(e){let t=[],r=e;for(;r.parent_run_id;){let e=this.runMap.get(r.parent_run_id);if(e)t.push(e),r=e;else break}return t}getBreadcrumbs(e){let t=[...this.getParents(e).reverse(),e].map((e,t,r)=>{let i=`${e.execution_order}:${e.run_type}:${e.name}`;return t===r.length-1?n(a.bold,i):i}).join(" > ");return n(u.grey,t)}onChainStart(e){let t=this.getBreadcrumbs(e);console.log(`${n(u.green,"[chain/start]")} [${t}] Entering Chain run with input: ${s(e.inputs,"[inputs]")}`)}onChainEnd(e){let t=this.getBreadcrumbs(e);console.log(`${n(u.cyan,"[chain/end]")} [${t}] [${l(e)}] Exiting Chain run with output: ${s(e.outputs,"[outputs]")}`)}onChainError(e){let t=this.getBreadcrumbs(e);console.log(`${n(u.red,"[chain/error]")} [${t}] [${l(e)}] Chain run errored with error: ${s(e.error,"[error]")}`)}onLLMStart(e){let t=this.getBreadcrumbs(e),r="prompts"in e.inputs?{prompts:e.inputs.prompts.map(e=>e.trim())}:e.inputs;console.log(`${n(u.green,"[llm/start]")} [${t}] Entering LLM run with input: ${s(r,"[inputs]")}`)}onLLMEnd(e){let t=this.getBreadcrumbs(e);console.log(`${n(u.cyan,"[llm/end]")} [${t}] [${l(e)}] Exiting LLM run with output: ${s(e.outputs,"[response]")}`)}onLLMError(e){let t=this.getBreadcrumbs(e);console.log(`${n(u.red,"[llm/error]")} [${t}] [${l(e)}] LLM run errored with error: ${s(e.error,"[error]")}`)}onToolStart(e){let t=this.getBreadcrumbs(e);console.log(`${n(u.green,"[tool/start]")} [${t}] Entering Tool run with input: "${o(e.inputs.input)}"`)}onToolEnd(e){let t=this.getBreadcrumbs(e);console.log(`${n(u.cyan,"[tool/end]")} [${t}] [${l(e)}] Exiting Tool run with output: "${o(e.outputs?.output)}"`)}onToolError(e){let t=this.getBreadcrumbs(e);console.log(`${n(u.red,"[tool/error]")} [${t}] [${l(e)}] Tool run errored with error: ${s(e.error,"[error]")}`)}onRetrieverStart(e){let t=this.getBreadcrumbs(e);console.log(`${n(u.green,"[retriever/start]")} [${t}] Entering Retriever run with input: ${s(e.inputs,"[inputs]")}`)}onRetrieverEnd(e){let t=this.getBreadcrumbs(e);console.log(`${n(u.cyan,"[retriever/end]")} [${t}] [${l(e)}] Exiting Retriever run with output: ${s(e.outputs,"[outputs]")}`)}onRetrieverError(e){let t=this.getBreadcrumbs(e);console.log(`${n(u.red,"[retriever/error]")} [${t}] [${l(e)}] Retriever run errored with error: ${s(e.error,"[error]")}`)}onAgentAction(e){let t=this.getBreadcrumbs(e);console.log(`${n(u.blue,"[agent/action]")} [${t}] Agent selected action: ${s(e.actions[e.actions.length-1],"[action]")}`)}}},55332:(e,t,r)=>{var a=r(81174);t.operation=function(e){return new a(t.timeouts(e),{forever:e&&(e.forever||e.retries===1/0),unref:e&&e.unref,maxRetryTime:e&&e.maxRetryTime})},t.timeouts=function(e){if(e instanceof Array)return[].concat(e);var t={retries:10,factor:2,minTimeout:1e3,maxTimeout:1/0,randomize:!1};for(var r in e)t[r]=e[r];if(t.minTimeout>t.maxTimeout)throw Error("minTimeout is greater than maxTimeout");for(var a=[],i=0;i<t.retries;i++)a.push(this.createTimeout(i,t));return e&&e.forever&&!a.length&&a.push(this.createTimeout(i,t)),a.sort(function(e,t){return e-t}),a},t.createTimeout=function(e,t){var r=Math.round((t.randomize?Math.random()+1:1)*Math.max(t.minTimeout,1)*Math.pow(t.factor,e));return Math.min(r,t.maxTimeout)},t.wrap=function(e,r,a){if(r instanceof Array&&(a=r,r=null),!a)for(var i in a=[],e)"function"==typeof e[i]&&a.push(i);for(var n=0;n<a.length;n++){var s=a[n],o=e[s];e[s]=(function(a){var i=t.operation(r),n=Array.prototype.slice.call(arguments,1),s=n.pop();n.push(function(e){i.retry(e)||(e&&(arguments[0]=i.mainError()),s.apply(this,arguments))}),i.attempt(function(){a.apply(e,n)})}).bind(e,o),e[s].options=r}}},56301:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{getEnv:()=>u,getEnvironmentVariable:()=>h,getRuntimeEnvironment:()=>c,getRuntimeEnvironmentSync:()=>d,isBrowser:()=>i,isDeno:()=>o,isJsDom:()=>s,isNode:()=>l,isWebWorker:()=>n});let i=()=>"undefined"!=typeof window&&void 0!==window.document,n=()=>"object"==typeof globalThis&&globalThis.constructor&&"DedicatedWorkerGlobalScope"===globalThis.constructor.name,s=()=>"undefined"!=typeof window&&"nodejs"===window.name||"undefined"!=typeof navigator&&navigator.userAgent.includes("jsdom"),o=()=>"undefined"!=typeof Deno,l=()=>"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node&&!o(),u=()=>{let e;return i()?"browser":l()?"node":n()?"webworker":s()?"jsdom":o()?"deno":"other"};async function c(){return d()}function d(){return void 0===a&&(a={library:"langchain-js",runtime:u()}),a}function h(e){try{if("undefined"!=typeof process)return process.env?.[e];if(o())return Deno?.env.get(e);return}catch(e){return}}},57161:(e,t,r)=>{"use strict";r.d(t,{AP:()=>s,FK:()=>n,mg:()=>i,vl:()=>o});var a=r(72892);class i extends a.XQ{static lc_name(){return"FunctionMessage"}constructor(e,t){"string"==typeof e&&(e={content:e,name:t}),super(e)}_getType(){return"function"}}class n extends a.gj{static lc_name(){return"FunctionMessageChunk"}_getType(){return"function"}concat(e){return new n({content:(0,a._I)(this.content,e.content),additional_kwargs:(0,a.ns)(this.additional_kwargs,e.additional_kwargs),response_metadata:(0,a.ns)(this.response_metadata,e.response_metadata),name:this.name??"",id:this.id??e.id})}}function s(e){return"function"===e._getType()}function o(e){return"function"===e._getType()}},57543:e=>{"use strict";e.exports=function(e,t){if("string"!=typeof e)throw TypeError("Expected a string");return t=void 0===t?"_":t,e.replace(/([a-z\d])([A-Z])/g,"$1"+t+"$2").replace(/([A-Z]+)([A-Z][a-z\d]+)/g,"$1"+t+"$2").toLowerCase()}},58361:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t,r=!1)=>{if(e instanceof a)return e;try{return new a(e,t)}catch(e){if(!r)return null;throw e}}},59438:(e,t,r)=>{"use strict";let a,i,n;r.d(t,{Az:()=>m,Ec:()=>p,Jz:()=>g,yk:()=>f});var s=r(30663);let o=()=>"undefined"!=typeof window&&void 0!==window.document,l=()=>"object"==typeof globalThis&&globalThis.constructor&&"DedicatedWorkerGlobalScope"===globalThis.constructor.name,u=()=>"undefined"!=typeof window&&"nodejs"===window.name||"undefined"!=typeof navigator&&navigator.userAgent.includes("jsdom"),c=()=>"undefined"!=typeof Deno,d=()=>"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node&&!c(),h=()=>a||(a=o()?"browser":d()?"node":l()?"webworker":u()?"jsdom":c()?"deno":"other");function p(){if(void 0===i){let e=h(),t=function(){if(void 0!==n)return n;let e={};for(let t of["VERCEL_GIT_COMMIT_SHA","NEXT_PUBLIC_VERCEL_GIT_COMMIT_SHA","COMMIT_REF","RENDER_GIT_COMMIT","CI_COMMIT_SHA","CIRCLE_SHA1","CF_PAGES_COMMIT_SHA","REACT_APP_GIT_SHA","SOURCE_VERSION","GITHUB_SHA","TRAVIS_COMMIT","GIT_COMMIT","BUILD_VCS_NUMBER","bamboo_planRepository_revision","Build.SourceVersion","BITBUCKET_COMMIT","DRONE_COMMIT_SHA","SEMAPHORE_GIT_SHA","BUILDKITE_COMMIT"]){let r=m(t);void 0!==r&&(e[t]=r)}return n=e,e}();i={library:"langsmith",runtime:e,sdk:"langsmith-js",sdk_version:s.Ls,...t}}return i}function f(){let e=function(){try{if("undefined"!=typeof process&&process.env)return Object.entries(process.env).reduce((e,[t,r])=>(e[t]=String(r),e),{});return}catch(e){return}}()||{},t={},r=["LANGCHAIN_API_KEY","LANGCHAIN_ENDPOINT","LANGCHAIN_TRACING_V2","LANGCHAIN_PROJECT","LANGCHAIN_SESSION","LANGSMITH_API_KEY","LANGSMITH_ENDPOINT","LANGSMITH_TRACING_V2","LANGSMITH_PROJECT","LANGSMITH_SESSION"];for(let[a,i]of Object.entries(e))(a.startsWith("LANGCHAIN_")||a.startsWith("LANGSMITH_"))&&"string"==typeof i&&!r.includes(a)&&!a.toLowerCase().includes("key")&&!a.toLowerCase().includes("secret")&&!a.toLowerCase().includes("token")&&("LANGCHAIN_REVISION_ID"===a?t.revision_id=i:t[a]=i);return t}function m(e){try{return"undefined"!=typeof process?process.env?.[e]:void 0}catch(e){return}}function g(e){return m(`LANGSMITH_${e}`)||m(`LANGCHAIN_${e}`)}},60157:(e,t,r)=>{"use strict";r.d(t,{BA:()=>w,EN:()=>function e(t){if(n(t))return n(t)&&"typeName"in t._def&&"ZodEffects"===t._def.typeName?e(t._def.schema):t;if(i(t))return i(t)&&"pipe"===t._zod.def.type?e(t._zod.def.in)??t:t;throw Error("Schema must be an instance of z3.ZodType or z4.$ZodType")},IS:()=>m,IU:()=>n,K3:()=>l,PA:()=>_,Pq:()=>i,QV:()=>b,RA:()=>E,W0:()=>v,X2:()=>p,Yi:()=>c,Zu:()=>d,c9:()=>o,cg:()=>h,gY:()=>g,hZ:()=>u,lm:()=>s,pE:()=>function e(t,r=!1){if(n(t))return t.strict();if(g(t)){let i=t._zod.def.shape;if(r)for(let[n,s]of Object.entries(t._zod.def.shape)){if(g(s)){let t=e(s,r);i[n]=t}else if(y(s)){let t=s._zod.def.element;g(t)&&(t=e(t,r)),i[n]=(0,a.o8B)(s,{...s._zod.def,element:t})}else i[n]=s;let t=a.fd.get(s);t&&a.fd.add(i[n],t)}let n=(0,a.o8B)(t,{...t._zod.def,shape:i,catchall:(0,a.G8g)(a.Umr)}),s=a.fd.get(t);return s&&a.fd.add(n,s),n}throw Error("Schema must be an instance of z3.ZodObject or z4.$ZodObject")},qT:()=>function e(t,r=!1){if(m(t))return t.passthrough();if(g(t)){let i=t._zod.def.shape;if(r)for(let[n,s]of Object.entries(t._zod.def.shape)){if(g(s)){let t=e(s,r);i[n]=t}else if(y(s)){let t=s._zod.def.element;g(t)&&(t=e(t,r)),i[n]=(0,a.o8B)(s,{...s._zod.def,element:t})}else i[n]=s;let t=a.fd.get(s);t&&a.fd.add(i[n],t)}let n=(0,a.o8B)(t,{...t._zod.def,shape:i,catchall:(0,a.emL)(a.GPR)}),s=a.fd.get(t);return s&&a.fd.add(n,s),n}throw Error("Schema must be an instance of z3.ZodObject or z4.$ZodObject")},yQ:()=>f,zL:()=>y});var a=r(31661);function i(e){if("object"!=typeof e||null===e||!("_zod"in e))return!1;let t=e._zod;return"object"==typeof t&&null!==t&&"def"in t}function n(e){if("object"!=typeof e||null===e||!("_def"in e)||"_zod"in e)return!1;let t=e._def;return"object"==typeof t&&null!=t&&"typeName"in t}function s(e){return i(e)&&console.warn("[WARNING] Attempting to use Zod 4 schema in a context where Zod 3 schema is expected. This may cause unexpected behavior."),n(e)}function o(e){return!(!e||"object"!=typeof e||Array.isArray(e))&&!!(i(e)||n(e))}async function l(e,t){if(i(e))try{let r=await (0,a.EJS)(e,t);return{success:!0,data:r}}catch(e){return{success:!1,error:e}}if(n(e))return e.safeParse(t);throw Error("Schema must be an instance of z3.ZodType or z4.$ZodType")}async function u(e,t){if(i(e))return(0,a.qgA)(e,t);if(n(e))return e.parse(t);throw Error("Schema must be an instance of z3.ZodType or z4.$ZodType")}function c(e,t){if(i(e))try{let r=(0,a.qgA)(e,t);return{success:!0,data:r}}catch(e){return{success:!1,error:e}}if(n(e))return e.safeParse(t);throw Error("Schema must be an instance of z3.ZodType or z4.$ZodType")}function d(e,t){if(i(e))return(0,a.qgA)(e,t);if(n(e))return e.parse(t);throw Error("Schema must be an instance of z3.ZodType or z4.$ZodType")}function h(e){return i(e)?a.fd.get(e)?.description:n(e)||"description"in e&&"string"==typeof e.description?e.description:void 0}function p(e){if(!o(e))return!1;if(n(e)){let t=e._def;if("ZodObject"===t.typeName)return!e.shape||0===Object.keys(e.shape).length;if("ZodRecord"===t.typeName)return!0}if(i(e)){let t=e._zod.def;if("object"===t.type)return!e.shape||0===Object.keys(e.shape).length;if("record"===t.type)return!0}return"object"==typeof e&&null!==e&&!("shape"in e)}function f(e){return!!o(e)&&(n(e)?"ZodString"===e._def.typeName:!!i(e)&&"string"===e._zod.def.type)}function m(e){return"object"==typeof e&&null!==e&&"_def"in e&&"object"==typeof e._def&&null!==e._def&&"typeName"in e._def&&"ZodObject"===e._def.typeName||!1}function g(e){return!!i(e)&&("object"==typeof e&&null!==e&&"_zod"in e&&"object"==typeof e._zod&&null!==e._zod&&"def"in e._zod&&"object"==typeof e._zod.def&&null!==e._zod.def&&"type"in e._zod.def&&"object"===e._zod.def.type||!1)}function y(e){return!!i(e)&&("object"==typeof e&&null!==e&&"_zod"in e&&"object"==typeof e._zod&&null!==e._zod&&"def"in e._zod&&"object"==typeof e._zod.def&&null!==e._zod.def&&"type"in e._zod.def&&"array"===e._zod.def.type||!1)}function b(e){return!!(m(e)||g(e))}function _(e){if(n(e))return e.shape;if(i(e))return e._zod.def.shape;throw Error("Schema must be an instance of z3.ZodObject or z4.$ZodObject")}function v(e,t){if(n(e))return e.extend(t);if(i(e))return a.ZSL.extend(e,t);throw Error("Schema must be an instance of z3.ZodObject or z4.$ZodObject")}function w(e){if(n(e))return e.partial();if(i(e))return a.ZSL.partial(a.igI,e,void 0);throw Error("Schema must be an instance of z3.ZodObject or z4.$ZodObject")}function E(e){if(n(e))try{let t=e.parse(void 0);return()=>t}catch{return}if(i(e))try{let t=(0,a.qgA)(e,void 0);return()=>t}catch{}}},60301:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>0>=a(e,t,r)},60662:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BaseCallbackHandler:()=>l,callbackHandlerPrefersStreaming:()=>o,isBaseCallbackHandler:()=>u});var a=r(82116),i=r(84902),n=r(56301);class s{}function o(e){return"lc_prefer_streaming"in e&&e.lc_prefer_streaming}class l extends s{get lc_namespace(){return["langchain_core","callbacks",this.name]}get lc_secrets(){}get lc_attributes(){}get lc_aliases(){}get lc_serializable_keys(){}static lc_name(){return this.name}get lc_id(){return[...this.lc_namespace,(0,i.get_lc_unique_name)(this.constructor)]}constructor(e){super(),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ignoreLLM",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreChain",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreAgent",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreRetriever",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"ignoreCustomEvent",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"raiseError",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"awaitHandlers",{enumerable:!0,configurable:!0,writable:!0,value:"false"===(0,n.getEnvironmentVariable)("LANGCHAIN_CALLBACKS_BACKGROUND")}),this.lc_kwargs=e||{},e&&(this.ignoreLLM=e.ignoreLLM??this.ignoreLLM,this.ignoreChain=e.ignoreChain??this.ignoreChain,this.ignoreAgent=e.ignoreAgent??this.ignoreAgent,this.ignoreRetriever=e.ignoreRetriever??this.ignoreRetriever,this.ignoreCustomEvent=e.ignoreCustomEvent??this.ignoreCustomEvent,this.raiseError=e.raiseError??this.raiseError,this.awaitHandlers=this.raiseError||(e._awaitHandler??this.awaitHandlers))}copy(){return new this.constructor(this)}toJSON(){return i.Serializable.prototype.toJSON.call(this)}toJSONNotImplemented(){return i.Serializable.prototype.toJSONNotImplemented.call(this)}static fromMethods(e){class t extends l{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:a.A()}),Object.assign(this,e)}}return new t}}let u=e=>void 0!==e&&"function"==typeof e.copy&&"string"==typeof e.name&&"boolean"==typeof e.awaitHandlers},62502:(e,t,r)=>{"use strict";let a=r(25706);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let n=(e,t,r)=>new Promise((n,s)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void n(e);let o=setTimeout(()=>{if("function"==typeof r){try{n(r())}catch(e){s(e)}return}let a="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,o=r instanceof Error?r:new i(a);"function"==typeof e.cancel&&e.cancel(),s(o)},t);a(e.then(n,s),()=>{clearTimeout(o)})});e.exports=n,e.exports.default=n,e.exports.TimeoutError=i},63391:(e,t,r)=>{"use strict";e=r.nmd(e);let a=(e=0)=>t=>`\u001B[${38+e};5;${t}m`,i=(e=0)=>(t,r,a)=>`\u001B[${38+e};2;${t};${r};${a}m`;Object.defineProperty(e,"exports",{enumerable:!0,get:function(){let e=new Map,t={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};for(let[r,a]of(t.color.gray=t.color.blackBright,t.bgColor.bgGray=t.bgColor.bgBlackBright,t.color.grey=t.color.blackBright,t.bgColor.bgGrey=t.bgColor.bgBlackBright,Object.entries(t))){for(let[r,i]of Object.entries(a))t[r]={open:`\u001B[${i[0]}m`,close:`\u001B[${i[1]}m`},a[r]=t[r],e.set(i[0],i[1]);Object.defineProperty(t,r,{value:a,enumerable:!1})}return Object.defineProperty(t,"codes",{value:e,enumerable:!1}),t.color.close="\x1b[39m",t.bgColor.close="\x1b[49m",t.color.ansi256=a(),t.color.ansi16m=i(),t.bgColor.ansi256=a(10),t.bgColor.ansi16m=i(10),Object.defineProperties(t,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:e=>{let t=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));if(!t)return[0,0,0];let{colorString:r}=t.groups;3===r.length&&(r=r.split("").map(e=>e+e).join(""));let a=Number.parseInt(r,16);return[a>>16&255,a>>8&255,255&a]},enumerable:!1},hexToAnsi256:{value:e=>t.rgbToAnsi256(...t.hexToRgb(e)),enumerable:!1}}),t}})},63611:(e,t,r)=>{"use strict";let a=r(23518),i=["Failed to fetch","NetworkError when attempting to fetch resource.","The Internet connection appears to be offline.","Network request failed"];class n extends Error{constructor(e){super(),e instanceof Error?(this.originalError=e,{message:e}=e):(this.originalError=Error(e),this.originalError.stack=this.stack),this.name="AbortError",this.message=e}}let s=(e,t,r)=>{let a=r.retries-(t-1);return e.attemptNumber=t,e.retriesLeft=a,e},o=e=>i.includes(e),l=(e,t)=>new Promise((r,i)=>{t={onFailedAttempt:()=>{},retries:10,...t};let l=a.operation(t);l.attempt(async a=>{try{r(await e(a))}catch(e){if(!(e instanceof Error))return void i(TypeError(`Non-error was thrown: "${e}". You should only throw errors.`));if(e instanceof n)l.stop(),i(e.originalError);else if(e instanceof TypeError&&!o(e.message))l.stop(),i(e);else{s(e,a,t);try{await t.onFailedAttempt(e)}catch(e){i(e);return}l.retry(e)||i(l.mainError())}}})});e.exports=l,e.exports.default=l,e.exports.AbortError=n},64487:(e,t,r)=>{"use strict";let a=r(38267),{MAX_LENGTH:i,MAX_SAFE_INTEGER:n}=r(32397),{safeRe:s,t:o}=r(26515),l=r(98300),{compareIdentifiers:u}=r(78668);class c{constructor(e,t){if(t=l(t),e instanceof c)if(!!t.loose===e.loose&&!!t.includePrerelease===e.includePrerelease)return e;else e=e.version;else if("string"!=typeof e)throw TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>i)throw TypeError(`version is longer than ${i} characters`);a("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;let r=e.trim().match(t.loose?s[o.LOOSE]:s[o.FULL]);if(!r)throw TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+r[1],this.minor=+r[2],this.patch=+r[3],this.major>n||this.major<0)throw TypeError("Invalid major version");if(this.minor>n||this.minor<0)throw TypeError("Invalid minor version");if(this.patch>n||this.patch<0)throw TypeError("Invalid patch version");r[4]?this.prerelease=r[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){let t=+e;if(t>=0&&t<n)return t}return e}):this.prerelease=[],this.build=r[5]?r[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(a("SemVer.compare",this.version,this.options,e),!(e instanceof c)){if("string"==typeof e&&e===this.version)return 0;e=new c(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof c||(e=new c(e,this.options)),u(this.major,e.major)||u(this.minor,e.minor)||u(this.patch,e.patch)}comparePre(e){if(e instanceof c||(e=new c(e,this.options)),this.prerelease.length&&!e.prerelease.length)return -1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{let r=this.prerelease[t],i=e.prerelease[t];if(a("prerelease compare",t,r,i),void 0===r&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===r)return -1;else if(r===i)continue;else return u(r,i)}while(++t)}compareBuild(e){e instanceof c||(e=new c(e,this.options));let t=0;do{let r=this.build[t],i=e.build[t];if(a("build compare",t,r,i),void 0===r&&void 0===i)return 0;if(void 0===i)return 1;if(void 0===r)return -1;else if(r===i)continue;else return u(r,i)}while(++t)}inc(e,t,r){if(e.startsWith("pre")){if(!t&&!1===r)throw Error("invalid increment argument: identifier is empty");if(t){let e=`-${t}`.match(this.options.loose?s[o.PRERELEASELOOSE]:s[o.PRERELEASE]);if(!e||e[1]!==t)throw Error(`invalid identifier: ${t}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,r),this.inc("pre",t,r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,r),this.inc("pre",t,r);break;case"release":if(0===this.prerelease.length)throw Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(0!==this.minor||0!==this.patch||0===this.prerelease.length)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(0!==this.patch||0===this.prerelease.length)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{let e=+!!Number(r);if(0===this.prerelease.length)this.prerelease=[e];else{let a=this.prerelease.length;for(;--a>=0;)"number"==typeof this.prerelease[a]&&(this.prerelease[a]++,a=-2);if(-1===a){if(t===this.prerelease.join(".")&&!1===r)throw Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let a=[t,e];!1===r&&(a=[t]),0===u(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=a):this.prerelease=a}break}default:throw Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=c},65109:(e,t,r)=>{"use strict";r.d(t,{gk:()=>a.gk});var a=r(11457)},65164:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BaseTracer:()=>u,isBaseTracer:()=>l});var a=r(65109),i=r(60662),n=r(56301);let s=e=>{if(e)return e.events=e.events??[],e.child_runs=e.child_runs??[],e};function o(e,t){return e&&!Array.isArray(e)&&"object"==typeof e?e:{[t]:e}}function l(e){return"function"==typeof e._addRunToRunMap}class u extends i.BaseCallbackHandler{constructor(e){super(...arguments),Object.defineProperty(this,"runMap",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"runTreeMap",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"usesRunTreeMap",{enumerable:!0,configurable:!0,writable:!0,value:!1})}copy(){return this}getRunById(e){if(void 0!==e)return this.usesRunTreeMap?s(this.runTreeMap.get(e)):this.runMap.get(e)}stringifyError(e){return e instanceof Error?e.message+(e?.stack?`

${e.stack}`:""):"string"==typeof e?e:`${e}`}_addChildRun(e,t){e.child_runs.push(t)}_addRunToRunMap(e){let t=function(e,t,r){let a=r.toFixed(0).slice(0,3).padStart(3,"0");return`${new Date(e).toISOString().slice(0,-1)}${a}Z`.replace(/[-:.]/g,"")+t}(e.start_time,e.id,e.execution_order),r={...e},i=this.getRunById(r.parent_run_id);if(void 0!==r.parent_run_id?i&&(this._addChildRun(i,r),i.child_execution_order=Math.max(i.child_execution_order,r.child_execution_order),r.trace_id=i.trace_id,void 0!==i.dotted_order&&(r.dotted_order=[i.dotted_order,t].join("."))):(r.trace_id=r.id,r.dotted_order=t),this.usesRunTreeMap){let e=function e(t,r){if(t)return new a.gk({...t,parent_run:e(r),child_runs:t.child_runs.map(t=>e(t)).filter(e=>void 0!==e),extra:{...t.extra,runtime:(0,n.getRuntimeEnvironmentSync)()},tracingEnabled:!1})}(r,i);void 0!==e&&this.runTreeMap.set(r.id,e)}else this.runMap.set(r.id,r);return r}async _endTrace(e){let t=void 0!==e.parent_run_id&&this.getRunById(e.parent_run_id);t?t.child_execution_order=Math.max(t.child_execution_order,e.child_execution_order):await this.persistRun(e),await this.onRunUpdate?.(e),this.usesRunTreeMap?this.runTreeMap.delete(e.id):this.runMap.delete(e.id)}_getExecutionOrder(e){let t=void 0!==e&&this.getRunById(e);return t?t.child_execution_order+1:1}_createRunForLLMStart(e,t,r,a,i,n,s,o){let l=this._getExecutionOrder(a),u=Date.now(),c=s?{...i,metadata:s}:i,d={id:r,name:o??e.id[e.id.length-1],parent_run_id:a,start_time:u,serialized:e,events:[{name:"start",time:new Date(u).toISOString()}],inputs:{prompts:t},execution_order:l,child_runs:[],child_execution_order:l,run_type:"llm",extra:c??{},tags:n||[]};return this._addRunToRunMap(d)}async handleLLMStart(e,t,r,a,i,n,s,o){let l=this.getRunById(r)??this._createRunForLLMStart(e,t,r,a,i,n,s,o);return await this.onRunCreate?.(l),await this.onLLMStart?.(l),l}_createRunForChatModelStart(e,t,r,a,i,n,s,o){let l=this._getExecutionOrder(a),u=Date.now(),c=s?{...i,metadata:s}:i,d={id:r,name:o??e.id[e.id.length-1],parent_run_id:a,start_time:u,serialized:e,events:[{name:"start",time:new Date(u).toISOString()}],inputs:{messages:t},execution_order:l,child_runs:[],child_execution_order:l,run_type:"llm",extra:c??{},tags:n||[]};return this._addRunToRunMap(d)}async handleChatModelStart(e,t,r,a,i,n,s,o){let l=this.getRunById(r)??this._createRunForChatModelStart(e,t,r,a,i,n,s,o);return await this.onRunCreate?.(l),await this.onLLMStart?.(l),l}async handleLLMEnd(e,t,r,a,i){let n=this.getRunById(t);if(!n||n?.run_type!=="llm")throw Error("No LLM run to end.");return n.end_time=Date.now(),n.outputs=e,n.events.push({name:"end",time:new Date(n.end_time).toISOString()}),n.extra={...n.extra,...i},await this.onLLMEnd?.(n),await this._endTrace(n),n}async handleLLMError(e,t,r,a,i){let n=this.getRunById(t);if(!n||n?.run_type!=="llm")throw Error("No LLM run to end.");return n.end_time=Date.now(),n.error=this.stringifyError(e),n.events.push({name:"error",time:new Date(n.end_time).toISOString()}),n.extra={...n.extra,...i},await this.onLLMError?.(n),await this._endTrace(n),n}_createRunForChainStart(e,t,r,a,i,n,s,o){let l=this._getExecutionOrder(a),u=Date.now(),c={id:r,name:o??e.id[e.id.length-1],parent_run_id:a,start_time:u,serialized:e,events:[{name:"start",time:new Date(u).toISOString()}],inputs:t,execution_order:l,child_execution_order:l,run_type:s??"chain",child_runs:[],extra:n?{metadata:n}:{},tags:i||[]};return this._addRunToRunMap(c)}async handleChainStart(e,t,r,a,i,n,s,o){let l=this.getRunById(r)??this._createRunForChainStart(e,t,r,a,i,n,s,o);return await this.onRunCreate?.(l),await this.onChainStart?.(l),l}async handleChainEnd(e,t,r,a,i){let n=this.getRunById(t);if(!n)throw Error("No chain run to end.");return n.end_time=Date.now(),n.outputs=o(e,"output"),n.events.push({name:"end",time:new Date(n.end_time).toISOString()}),i?.inputs!==void 0&&(n.inputs=o(i.inputs,"input")),await this.onChainEnd?.(n),await this._endTrace(n),n}async handleChainError(e,t,r,a,i){let n=this.getRunById(t);if(!n)throw Error("No chain run to end.");return n.end_time=Date.now(),n.error=this.stringifyError(e),n.events.push({name:"error",time:new Date(n.end_time).toISOString()}),i?.inputs!==void 0&&(n.inputs=o(i.inputs,"input")),await this.onChainError?.(n),await this._endTrace(n),n}_createRunForToolStart(e,t,r,a,i,n,s){let o=this._getExecutionOrder(a),l=Date.now(),u={id:r,name:s??e.id[e.id.length-1],parent_run_id:a,start_time:l,serialized:e,events:[{name:"start",time:new Date(l).toISOString()}],inputs:{input:t},execution_order:o,child_execution_order:o,run_type:"tool",child_runs:[],extra:n?{metadata:n}:{},tags:i||[]};return this._addRunToRunMap(u)}async handleToolStart(e,t,r,a,i,n,s){let o=this.getRunById(r)??this._createRunForToolStart(e,t,r,a,i,n,s);return await this.onRunCreate?.(o),await this.onToolStart?.(o),o}async handleToolEnd(e,t){let r=this.getRunById(t);if(!r||r?.run_type!=="tool")throw Error("No tool run to end");return r.end_time=Date.now(),r.outputs={output:e},r.events.push({name:"end",time:new Date(r.end_time).toISOString()}),await this.onToolEnd?.(r),await this._endTrace(r),r}async handleToolError(e,t){let r=this.getRunById(t);if(!r||r?.run_type!=="tool")throw Error("No tool run to end");return r.end_time=Date.now(),r.error=this.stringifyError(e),r.events.push({name:"error",time:new Date(r.end_time).toISOString()}),await this.onToolError?.(r),await this._endTrace(r),r}async handleAgentAction(e,t){let r=this.getRunById(t);r&&r?.run_type==="chain"&&(r.actions=r.actions||[],r.actions.push(e),r.events.push({name:"agent_action",time:new Date().toISOString(),kwargs:{action:e}}),await this.onAgentAction?.(r))}async handleAgentEnd(e,t){let r=this.getRunById(t);r&&r?.run_type==="chain"&&(r.events.push({name:"agent_end",time:new Date().toISOString(),kwargs:{action:e}}),await this.onAgentEnd?.(r))}_createRunForRetrieverStart(e,t,r,a,i,n,s){let o=this._getExecutionOrder(a),l=Date.now(),u={id:r,name:s??e.id[e.id.length-1],parent_run_id:a,start_time:l,serialized:e,events:[{name:"start",time:new Date(l).toISOString()}],inputs:{query:t},execution_order:o,child_execution_order:o,run_type:"retriever",child_runs:[],extra:n?{metadata:n}:{},tags:i||[]};return this._addRunToRunMap(u)}async handleRetrieverStart(e,t,r,a,i,n,s){let o=this.getRunById(r)??this._createRunForRetrieverStart(e,t,r,a,i,n,s);return await this.onRunCreate?.(o),await this.onRetrieverStart?.(o),o}async handleRetrieverEnd(e,t){let r=this.getRunById(t);if(!r||r?.run_type!=="retriever")throw Error("No retriever run to end");return r.end_time=Date.now(),r.outputs={documents:e},r.events.push({name:"end",time:new Date(r.end_time).toISOString()}),await this.onRetrieverEnd?.(r),await this._endTrace(r),r}async handleRetrieverError(e,t){let r=this.getRunById(t);if(!r||r?.run_type!=="retriever")throw Error("No retriever run to end");return r.end_time=Date.now(),r.error=this.stringifyError(e),r.events.push({name:"error",time:new Date(r.end_time).toISOString()}),await this.onRetrieverError?.(r),await this._endTrace(r),r}async handleText(e,t){let r=this.getRunById(t);r&&r?.run_type==="chain"&&(r.events.push({name:"text",time:new Date().toISOString(),kwargs:{text:e}}),await this.onText?.(r))}async handleLLMNewToken(e,t,r,a,i,n){let s=this.getRunById(r);if(!s||s?.run_type!=="llm")throw Error('Invalid "runId" provided to "handleLLMNewToken" callback.');return s.events.push({name:"new_token",time:new Date().toISOString(),kwargs:{token:e,idx:t,chunk:n?.chunk}}),await this.onLLMNewToken?.(s,e,{chunk:n?.chunk}),s}}},65381:(e,t,r)=>{"use strict";r.d(t,{Kj:()=>C});var a=r(82116),i=r(63611),n=r(71719),s=r(17266);let o=[400,401,403,404,405,406,407,408],l=[409];class u{constructor(e){Object.defineProperty(this,"maxConcurrency",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"maxRetries",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"queue",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"onFailedResponseHook",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"debug",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxConcurrency=e.maxConcurrency??1/0,this.maxRetries=e.maxRetries??6,this.debug=e.debug,this.queue=new n.default({concurrency:this.maxConcurrency}),this.onFailedResponseHook=e?.onFailedResponseHook}call(e,...t){let r=this.onFailedResponseHook;return this.queue.add(()=>i(()=>e(...t).catch(e=>{if(e instanceof Error)throw e;throw Error(e)}),{async onFailedAttempt(e){if(e.message.startsWith("Cancel")||e.message.startsWith("TimeoutError")||e.message.startsWith("AbortError")||e?.code==="ECONNABORTED")throw e;let t=e?.response,a=t?.status;if(a){if(o.includes(+a))throw e;if(l.includes(+a))return;r&&await r(t)}},retries:this.maxRetries,randomize:!0}),{throwOnTimeout:!0})}callWithOptions(e,t,...r){return e.signal?Promise.race([this.call(t,...r),new Promise((t,r)=>{e.signal?.addEventListener("abort",()=>{r(Error("AbortError"))})})]):this.call(t,...r)}fetch(...e){return this.call(()=>(0,s.Yx)(this.debug)(...e).then(e=>e.ok?e:Promise.reject(e)))}}function c(e){return"function"==typeof e?._getType}function d(e){let t={type:e._getType(),data:{content:e.content}};return e?.additional_kwargs&&Object.keys(e.additional_kwargs).length>0&&(t.data.additional_kwargs={...e.additional_kwargs}),t}var h=r(59438),p=r(30663),f=r(73726);function m(e,t){if(!f.A(e))throw Error(void 0!==t?`Invalid UUID for ${t}: ${e}`:`Invalid UUID: ${e}`);return e}var g=r(74081);function y(e){if(!e||e.split("/").length>2||e.startsWith("/")||e.endsWith("/")||e.split(":").length>2)throw Error(`Invalid identifier format: ${e}`);let[t,r]=e.split(":"),a=r||"latest";if(t.includes("/")){let[r,i]=t.split("/",2);if(!r||!i)throw Error(`Invalid identifier format: ${e}`);return[r,i,a]}if(!t)throw Error(`Invalid identifier format: ${e}`);return["-",t,a]}r(28584);class b extends Error{constructor(e){super(e),Object.defineProperty(this,"status",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name="LangSmithConflictError",this.status=409}}async function _(e,t,r){let a;if(e.ok){r&&(a=await e.text());return}a=await e.text();let i=`Failed to ${t}. Received status [${e.status}]: ${e.statusText}. Server response: ${a}`;if(409===e.status)throw new b(i);let n=Error(i);throw n.status=e.status,n}var v={result:"[Circular]"},w=[],E=[];let O=new TextEncoder;function $(e){return O.encode(e)}function I(e,t,r,a,i){try{let t=JSON.stringify(e,r,a);return $(t)}catch(s){let n;if(!s.message?.includes("Converting circular structure to JSON"))return console.warn(`[WARNING]: LangSmith received unserializable value.${t?`
Context: ${t}`:""}`),$("[Unserializable]");console.warn(`[WARNING]: LangSmith received circular JSON. This will decrease tracer performance. ${t?`
Context: ${t}`:""}`),void 0===i&&(i={depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}),function e(t,r,a,i,n,s,o){if(s+=1,"object"==typeof t&&null!==t){for(l=0;l<i.length;l++)if(i[l]===t)return void S(v,t,r,n);if(void 0!==o.depthLimit&&s>o.depthLimit||void 0!==o.edgesLimit&&a+1>o.edgesLimit)return void S("[...]",t,r,n);if(i.push(t),Array.isArray(t))for(l=0;l<t.length;l++)e(t[l],l,l,i,t,s,o);else{var l,u=Object.keys(t);for(l=0;l<u.length;l++){var c=u[l];e(t[c],c,l,i,t,s,o)}}i.pop()}}(e,"",0,[],void 0,0,i);try{n=0===E.length?JSON.stringify(e,r,a):JSON.stringify(e,function(e){return e=void 0!==e?e:function(e,t){return t},function(t,r){if(E.length>0)for(var a=0;a<E.length;a++){var i=E[a];if(i[1]===t&&i[0]===r){r=i[2],E.splice(a,1);break}}return e.call(this,t,r)}}(r),a)}catch(e){return $("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==w.length;){let e=w.pop();4===e.length?Object.defineProperty(e[0],e[1],e[3]):e[0][e[1]]=e[2]}}return $(n)}}function S(e,t,r,a){var i=Object.getOwnPropertyDescriptor(a,r);void 0!==i.get?i.configurable?(Object.defineProperty(a,r,{value:e}),w.push([a,r,t,i])):E.push([t,r,e]):(a[r]=e,w.push([a,r,t]))}function k(e){let t=(0,h.Ec)(),r=(0,h.yk)(),a=e.extra??{},i=a.metadata;return e.extra={...a,runtime:{...t,...a?.runtime},metadata:{...r,...r.revision_id||e.revision_id?{revision_id:e.revision_id??r.revision_id}:{},...i}},e}let A=e=>{let t=e?.toString()??(0,h.Jz)("TRACING_SAMPLING_RATE");if(void 0===t)return;let r=parseFloat(t);if(r<0||r>1)throw Error(`LANGSMITH_TRACING_SAMPLING_RATE must be between 0 and 1 if set. Got: ${r}`);return r},T=e=>{let t=e.replace("http://","").replace("https://","").split("/")[0].split(":")[0];return"localhost"===t||"127.0.0.1"===t||"::1"===t};async function x(e){let t=[];for await(let r of e)t.push(r);return t}function j(e){if(void 0!==e)return e.trim().replace(/^"(.*)"$/,"$1").replace(/^'(.*)'$/,"$1")}let P=async e=>{if(e?.status===429){let t=1e3*parseInt(e.headers.get("retry-after")??"30",10);if(t>0)return await new Promise(e=>setTimeout(e,t)),!0}return!1};function R(e){return"number"==typeof e?Number(e.toFixed(4)):e}class N{constructor(){Object.defineProperty(this,"items",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"sizeBytes",{enumerable:!0,configurable:!0,writable:!0,value:0})}peek(){return this.items[0]}push(e){let t,r=new Promise(e=>{t=e}),a=I(e.item,`Serializing run with id: ${e.item.id}`).length;return this.items.push({action:e.action,payload:e.item,itemPromiseResolve:t,itemPromise:r,size:a}),this.sizeBytes+=a,r}pop(e){if(e<1)throw Error("Number of bytes to pop off may not be less than 1.");let t=[],r=0;for(;r+(this.peek()?.size??0)<e&&this.items.length>0;){let e=this.items.shift();e&&(t.push(e),r+=e.size,this.sizeBytes-=e.size)}if(0===t.length&&this.items.length>0){let e=this.items.shift();t.push(e),r+=e.size,this.sizeBytes-=e.size}return[t.map(e=>({action:e.action,item:e.payload})),()=>t.forEach(e=>e.itemPromiseResolve())]}}class C{constructor(e={}){Object.defineProperty(this,"apiKey",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"apiUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"webUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"caller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"batchIngestCaller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"timeout_ms",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_tenantId",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"hideInputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"hideOutputs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tracingSampleRate",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"filteredPostUuids",{enumerable:!0,configurable:!0,writable:!0,value:new Set}),Object.defineProperty(this,"autoBatchTracing",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"autoBatchQueue",{enumerable:!0,configurable:!0,writable:!0,value:new N}),Object.defineProperty(this,"autoBatchTimeout",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"autoBatchAggregationDelayMs",{enumerable:!0,configurable:!0,writable:!0,value:250}),Object.defineProperty(this,"batchSizeBytesLimit",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"fetchOptions",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"settings",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"blockOnRootRunFinalization",{enumerable:!0,configurable:!0,writable:!0,value:"false"===(0,h.Az)("LANGSMITH_TRACING_BACKGROUND")}),Object.defineProperty(this,"traceBatchConcurrency",{enumerable:!0,configurable:!0,writable:!0,value:5}),Object.defineProperty(this,"_serverInfo",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_getServerInfoPromise",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"manualFlushMode",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"debug",{enumerable:!0,configurable:!0,writable:!0,value:"true"===(0,h.Az)("LANGSMITH_DEBUG")});let t=C.getDefaultClientConfig();if(this.tracingSampleRate=A(e.tracingSamplingRate),this.apiUrl=j(e.apiUrl??t.apiUrl)??"",this.apiUrl.endsWith("/")&&(this.apiUrl=this.apiUrl.slice(0,-1)),this.apiKey=j(e.apiKey??t.apiKey),this.webUrl=j(e.webUrl??t.webUrl),this.webUrl?.endsWith("/")&&(this.webUrl=this.webUrl.slice(0,-1)),this.timeout_ms=e.timeout_ms??9e4,this.caller=new u({...e.callerOptions??{},debug:e.debug??this.debug}),this.traceBatchConcurrency=e.traceBatchConcurrency??this.traceBatchConcurrency,this.traceBatchConcurrency<1)throw Error("Trace batch concurrency must be positive.");this.debug=e.debug??this.debug,this.batchIngestCaller=new u({maxRetries:2,maxConcurrency:this.traceBatchConcurrency,...e.callerOptions??{},onFailedResponseHook:P,debug:e.debug??this.debug}),this.hideInputs=e.hideInputs??e.anonymizer??t.hideInputs,this.hideOutputs=e.hideOutputs??e.anonymizer??t.hideOutputs,this.autoBatchTracing=e.autoBatchTracing??this.autoBatchTracing,this.blockOnRootRunFinalization=e.blockOnRootRunFinalization??this.blockOnRootRunFinalization,this.batchSizeBytesLimit=e.batchSizeBytesLimit,this.fetchOptions=e.fetchOptions||{},this.manualFlushMode=e.manualFlushMode??this.manualFlushMode}static getDefaultClientConfig(){let e=(0,h.Jz)("API_KEY"),t=(0,h.Jz)("ENDPOINT")??"https://api.smith.langchain.com";return{apiUrl:t,apiKey:e,webUrl:void 0,hideInputs:"true"===(0,h.Jz)("HIDE_INPUTS"),hideOutputs:"true"===(0,h.Jz)("HIDE_OUTPUTS")}}getHostUrl(){if(this.webUrl)return this.webUrl;if(T(this.apiUrl))return this.webUrl="http://localhost:3000",this.webUrl;if(this.apiUrl.endsWith("/api/v1"))return this.webUrl=this.apiUrl.replace("/api/v1",""),this.webUrl;if(this.apiUrl.includes("/api")&&!this.apiUrl.split(".",1)[0].endsWith("api"))return this.webUrl=this.apiUrl.replace("/api",""),this.webUrl;if(this.apiUrl.split(".",1)[0].includes("dev"))return this.webUrl="https://dev.smith.langchain.com",this.webUrl;else if(this.apiUrl.split(".",1)[0].includes("eu"))return this.webUrl="https://eu.smith.langchain.com",this.webUrl;else if(this.apiUrl.split(".",1)[0].includes("beta"))return this.webUrl="https://beta.smith.langchain.com",this.webUrl;else return this.webUrl="https://smith.langchain.com",this.webUrl}get headers(){let e={"User-Agent":`langsmith-js/${p.Ls}`};return this.apiKey&&(e["x-api-key"]=`${this.apiKey}`),e}async processInputs(e){return!1===this.hideInputs?e:!0===this.hideInputs?{}:"function"==typeof this.hideInputs?this.hideInputs(e):e}async processOutputs(e){return!1===this.hideOutputs?e:!0===this.hideOutputs?{}:"function"==typeof this.hideOutputs?this.hideOutputs(e):e}async prepareRunCreateOrUpdateInputs(e){let t={...e};return void 0!==t.inputs&&(t.inputs=await this.processInputs(t.inputs)),void 0!==t.outputs&&(t.outputs=await this.processOutputs(t.outputs)),t}async _getResponse(e,t){let r=t?.toString()??"",a=`${this.apiUrl}${e}?${r}`,i=await this.caller.call((0,s.Yx)(this.debug),a,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(i,`Failed to fetch ${e}`),i}async _get(e,t){return(await this._getResponse(e,t)).json()}async *_getPaginated(e,t=new URLSearchParams,r){let a=Number(t.get("offset"))||0,i=Number(t.get("limit"))||100;for(;;){t.set("offset",String(a)),t.set("limit",String(i));let n=`${this.apiUrl}${e}?${t}`,o=await this.caller.call((0,s.Yx)(this.debug),n,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(o,`Failed to fetch ${e}`);let l=r?r(await o.json()):await o.json();if(0===l.length||(yield l,l.length<i))break;a+=l.length}}async *_getCursorPaginatedList(e,t=null,r="POST",a="runs"){let i=t?{...t}:{};for(;;){let t=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}${e}`,{method:r,headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions,body:JSON.stringify(i)}),n=await t.json();if(!n||!n[a])break;yield n[a];let o=n.cursors;if(!o||!o.next)break;i.cursor=o.next}}_shouldSample(){return void 0===this.tracingSampleRate||Math.random()<this.tracingSampleRate}_filterForSampling(e,t=!1){if(void 0===this.tracingSampleRate)return e;if(t){let t=[];for(let r of e)this.filteredPostUuids.has(r.id)?this.filteredPostUuids.delete(r.id):t.push(r);return t}{let t=[];for(let r of e){let e=r.trace_id??r.id;this.filteredPostUuids.has(e)||(r.id===e?this._shouldSample()?t.push(r):this.filteredPostUuids.add(e):t.push(r))}return t}}async _getBatchSizeLimitBytes(){let e=await this._ensureServerInfo();return this.batchSizeBytesLimit??e.batch_ingest_config?.size_limit_bytes??0x1400000}async _getMultiPartSupport(){let e=await this._ensureServerInfo();return e.instance_flags?.dataset_examples_multipart_enabled??!1}drainAutoBatchQueue(e){let t=[];for(;this.autoBatchQueue.items.length>0;){let[r,a]=this.autoBatchQueue.pop(e);if(!r.length){a();break}let i=this._processBatch(r,a).catch(console.error);t.push(i)}return Promise.all(t)}async _processBatch(e,t){if(!e.length)return void t();try{let t={runCreates:e.filter(e=>"create"===e.action).map(e=>e.item),runUpdates:e.filter(e=>"update"===e.action).map(e=>e.item)},r=await this._ensureServerInfo();r?.batch_ingest_config?.use_multipart_endpoint?await this.multipartIngestRuns(t):await this.batchIngestRuns(t)}finally{t()}}async processRunOperation(e){clearTimeout(this.autoBatchTimeout),this.autoBatchTimeout=void 0,"create"===e.action&&(e.item=k(e.item));let t=this.autoBatchQueue.push(e);if(this.manualFlushMode)return t;let r=await this._getBatchSizeLimitBytes();return this.autoBatchQueue.sizeBytes>r&&this.drainAutoBatchQueue(r),this.autoBatchQueue.items.length>0&&(this.autoBatchTimeout=setTimeout(()=>{this.autoBatchTimeout=void 0,this.drainAutoBatchQueue(r)},this.autoBatchAggregationDelayMs)),t}async _getServerInfo(){let e=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/info`,{method:"GET",headers:{Accept:"application/json"},signal:AbortSignal.timeout(2500),...this.fetchOptions});await _(e,"get server info");let t=await e.json();return this.debug&&console.log("\n=== LangSmith Server Configuration ===\n"+JSON.stringify(t,null,2)+"\n"),t}async _ensureServerInfo(){return void 0===this._getServerInfoPromise&&(this._getServerInfoPromise=(async()=>{if(void 0===this._serverInfo)try{this._serverInfo=await this._getServerInfo()}catch(e){console.warn(`[WARNING]: LangSmith failed to fetch info on supported operations with status code ${e.status}. Falling back to batch operations and default limits.`)}return this._serverInfo??{}})()),this._getServerInfoPromise.then(e=>(void 0===this._serverInfo&&(this._getServerInfoPromise=void 0),e))}async _getSettings(){return this.settings||(this.settings=this._get("/settings")),await this.settings}async flush(){let e=await this._getBatchSizeLimitBytes();await this.drainAutoBatchQueue(e)}async createRun(e){if(!this._filterForSampling([e]).length)return;let t={...this.headers,"Content-Type":"application/json"},r=e.project_name;delete e.project_name;let a=await this.prepareRunCreateOrUpdateInputs({session_name:r,...e,start_time:e.start_time??Date.now()});if(this.autoBatchTracing&&void 0!==a.trace_id&&void 0!==a.dotted_order)return void this.processRunOperation({action:"create",item:a}).catch(console.error);let i=k(a),n=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/runs`,{method:"POST",headers:t,body:I(i,`Creating run with id: ${i.id}`),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(n,"create run",!0)}async batchIngestRuns({runCreates:e,runUpdates:t}){if(void 0===e&&void 0===t)return;let r=await Promise.all(e?.map(e=>this.prepareRunCreateOrUpdateInputs(e))??[]),a=await Promise.all(t?.map(e=>this.prepareRunCreateOrUpdateInputs(e))??[]);if(r.length>0&&a.length>0){let e=r.reduce((e,t)=>(t.id&&(e[t.id]=t),e),{}),t=[];for(let r of a)void 0!==r.id&&e[r.id]?e[r.id]={...e[r.id],...r}:t.push(r);r=Object.values(e),a=t}let i={post:r,patch:a};if(!i.post.length&&!i.patch.length)return;let n={post:[],patch:[]};for(let e of["post","patch"]){let t=i[e].reverse(),r=t.pop();for(;void 0!==r;)n[e].push(r),r=t.pop()}if(n.post.length>0||n.patch.length>0){let e=n.post.map(e=>e.id).concat(n.patch.map(e=>e.id)).join(",");await this._postBatchIngestRuns(I(n,`Ingesting runs with ids: ${e}`))}}async _postBatchIngestRuns(e){let t={...this.headers,"Content-Type":"application/json",Accept:"application/json"},r=await this.batchIngestCaller.call((0,s.Yx)(this.debug),`${this.apiUrl}/runs/batch`,{method:"POST",headers:t,body:e,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(r,"batch create run",!0)}async multipartIngestRuns({runCreates:e,runUpdates:t}){if(void 0===e&&void 0===t)return;let r={},a=[];for(let t of e??[]){let e=await this.prepareRunCreateOrUpdateInputs(t);void 0!==e.id&&void 0!==e.attachments&&(r[e.id]=e.attachments),delete e.attachments,a.push(e)}let i=[];for(let e of t??[])i.push(await this.prepareRunCreateOrUpdateInputs(e));if(void 0!==a.find(e=>void 0===e.trace_id||void 0===e.dotted_order))throw Error('Multipart ingest requires "trace_id" and "dotted_order" to be set when creating a run');if(void 0!==i.find(e=>void 0===e.trace_id||void 0===e.dotted_order))throw Error('Multipart ingest requires "trace_id" and "dotted_order" to be set when updating a run');if(a.length>0&&i.length>0){let e=a.reduce((e,t)=>(t.id&&(e[t.id]=t),e),{}),t=[];for(let r of i)void 0!==r.id&&e[r.id]?e[r.id]={...e[r.id],...r}:t.push(r);a=Object.values(e),i=t}if(0===a.length&&0===i.length)return;let n=[],s=[];for(let[e,t]of[["post",a],["patch",i]])for(let a of t){let{inputs:t,outputs:i,events:o,attachments:l,...u}=a,c={inputs:t,outputs:i,events:o},d=I(u,`Serializing for multipart ingestion of run with id: ${u.id}`);for(let[t,r]of(s.push({name:`${e}.${u.id}`,payload:new Blob([d],{type:`application/json; length=${d.length}`})}),Object.entries(c))){if(void 0===r)continue;let a=I(r,`Serializing ${t} for multipart ingestion of run with id: ${u.id}`);s.push({name:`${e}.${u.id}.${t}`,payload:new Blob([a],{type:`application/json; length=${a.length}`})})}if(void 0!==u.id){let e=r[u.id];if(e)for(let[t,a]of(delete r[u.id],Object.entries(e))){let e,r;if(Array.isArray(a)?[e,r]=a:(e=a.mimeType,r=a.data),t.includes(".")){console.warn(`Skipping attachment '${t}' for run ${u.id}: Invalid attachment name. Attachment names must not contain periods ('.'). Please rename the attachment and try again.`);continue}s.push({name:`attachment.${u.id}.${t}`,payload:new Blob([r],{type:`${e}; length=${r.byteLength}`})})}}n.push(`trace=${u.trace_id},id=${u.id}`)}await this._sendMultipartRequest(s,n.join("; "))}async _createNodeFetchBody(e,t){let r=[];for(let a of e)r.push(new Blob([`--${t}\r
`])),r.push(new Blob([`Content-Disposition: form-data; name="${a.name}"\r
`,`Content-Type: ${a.payload.type}\r
\r
`])),r.push(a.payload),r.push(new Blob(["\r\n"]));r.push(new Blob([`--${t}--\r
`]));let a=new Blob(r);return await a.arrayBuffer()}async _createMultipartStream(e,t){let r=new TextEncoder;return new ReadableStream({async start(a){let i=async e=>{"string"==typeof e?a.enqueue(r.encode(e)):a.enqueue(e)};for(let r of e){await i(`--${t}\r
`),await i(`Content-Disposition: form-data; name="${r.name}"\r
`),await i(`Content-Type: ${r.payload.type}\r
\r
`);let e=r.payload.stream().getReader();try{let t;for(;!(t=await e.read()).done;)a.enqueue(t.value)}finally{e.releaseLock()}await i("\r\n")}await i(`--${t}--\r
`),a.close()}})}async _sendMultipartRequest(e,t){try{let t="----LangSmithFormBoundary"+Math.random().toString(36).slice(2),r=await ((0,s.rO)()?this._createNodeFetchBody(e,t):this._createMultipartStream(e,t)),a=await this.batchIngestCaller.call((0,s.Yx)(this.debug),`${this.apiUrl}/runs/multipart`,{method:"POST",headers:{...this.headers,"Content-Type":`multipart/form-data; boundary=${t}`},body:r,duplex:"half",signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(a,"ingest multipart runs",!0)}catch(e){console.warn(`${e.message.trim()}

Context: ${t}`)}}async updateRun(e,t){m(e),t.inputs&&(t.inputs=await this.processInputs(t.inputs)),t.outputs&&(t.outputs=await this.processOutputs(t.outputs));let r={...t,id:e};if(!this._filterForSampling([r],!0).length)return;if(this.autoBatchTracing&&void 0!==r.trace_id&&void 0!==r.dotted_order)return void 0!==t.end_time&&void 0===r.parent_run_id&&this.blockOnRootRunFinalization&&!this.manualFlushMode?void await this.processRunOperation({action:"update",item:r}).catch(console.error):void this.processRunOperation({action:"update",item:r}).catch(console.error);let a={...this.headers,"Content-Type":"application/json"},i=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/runs/${e}`,{method:"PATCH",headers:a,body:I(t,`Serializing payload to update run with id: ${e}`),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(i,"update run",!0)}async readRun(e,{loadChildRuns:t}={loadChildRuns:!1}){m(e);let r=await this._get(`/runs/${e}`);return t&&(r=await this._loadChildRuns(r)),r}async getRunUrl({runId:e,run:t,projectOpts:r}){if(void 0!==t){let e;e=t.session_id?t.session_id:r?.projectName?(await this.readProject({projectName:r?.projectName})).id:r?.projectId?r?.projectId:(await this.readProject({projectName:(0,h.Jz)("PROJECT")||"default"})).id;let a=await this._getTenantId();return`${this.getHostUrl()}/o/${a}/projects/p/${e}/r/${t.id}?poll=true`}if(void 0!==e){let t=await this.readRun(e);if(!t.app_path)throw Error(`Run ${e} has no app_path`);let r=this.getHostUrl();return`${r}${t.app_path}`}throw Error("Must provide either runId or run")}async _loadChildRuns(e){let t=await x(this.listRuns({isRoot:!1,projectId:e.session_id,traceId:e.trace_id})),r={},a={};for(let i of(t.sort((e,t)=>(e?.dotted_order??"").localeCompare(t?.dotted_order??"")),t)){if(null===i.parent_run_id||void 0===i.parent_run_id)throw Error(`Child run ${i.id} has no parent`);i.dotted_order?.startsWith(e.dotted_order??"")&&i.id!==e.id&&(i.parent_run_id in r||(r[i.parent_run_id]=[]),r[i.parent_run_id].push(i),a[i.id]=i)}for(let t in e.child_runs=r[e.id]||[],r)t!==e.id&&(a[t].child_runs=r[t]);return e}async *listRuns(e){let{projectId:t,projectName:r,parentRunId:a,traceId:i,referenceExampleId:n,startTime:s,executionOrder:o,isRoot:l,runType:u,error:c,id:d,query:h,filter:p,traceFilter:f,treeFilter:m,limit:g,select:y,order:b}=e,_=[];if(t&&(_=Array.isArray(t)?t:[t]),r){let e=Array.isArray(r)?r:[r],t=await Promise.all(e.map(e=>this.readProject({projectName:e}).then(e=>e.id)));_.push(...t)}let v={session:_.length?_:null,run_type:u,reference_example:n,query:h,filter:p,trace_filter:f,tree_filter:m,execution_order:o,parent_run:a,start_time:s?s.toISOString():null,error:c,id:d,limit:g,trace:i,select:y||["app_path","completion_cost","completion_tokens","dotted_order","end_time","error","events","extra","feedback_stats","first_token_time","id","inputs","name","outputs","parent_run_id","parent_run_ids","prompt_cost","prompt_tokens","reference_example_id","run_type","session_id","start_time","status","tags","total_cost","total_tokens","trace_id"],is_root:l,order:b},w=0;for await(let e of this._getCursorPaginatedList("/runs/query",v))if(g){if(w>=g)break;if(e.length+w>g){let t=e.slice(0,g-w);yield*t;break}w+=e.length,yield*e}else yield*e}async *listGroupRuns(e){let{projectId:t,projectName:r,groupBy:a,filter:i,startTime:n,endTime:o,limit:l,offset:u}=e,c={session_id:t||(await this.readProject({projectName:r})).id,group_by:a,filter:i,start_time:n?n.toISOString():null,end_time:o?o.toISOString():null,limit:Number(l)||100},d=Number(u)||0,h="/runs/group",p=`${this.apiUrl}${h}`;for(;;){let e=Object.fromEntries(Object.entries({...c,offset:d}).filter(([e,t])=>void 0!==t)),t=await this.caller.call((0,s.Yx)(),p,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(e),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(t,`Failed to fetch ${h}`);let{groups:r,total:a}=await t.json();if(0===r.length)break;for(let e of r)yield e;if((d+=r.length)>=a)break}}async getRunStats({id:e,trace:t,parentRun:r,runType:a,projectNames:i,projectIds:n,referenceExampleIds:o,startTime:l,endTime:u,error:c,query:d,filter:h,traceFilter:p,treeFilter:f,isRoot:m,dataSourceType:g}){let y=n||[];i&&(y=[...n||[],...await Promise.all(i.map(e=>this.readProject({projectName:e}).then(e=>e.id)))]);let b=Object.fromEntries(Object.entries({id:e,trace:t,parent_run:r,run_type:a,session:y,reference_example:o,start_time:l,end_time:u,error:c,query:d,filter:h,trace_filter:p,tree_filter:f,is_root:m,data_source_type:g}).filter(([e,t])=>void 0!==t)),_=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/runs/stats`,{method:"POST",headers:this.headers,body:JSON.stringify(b),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _.json()}async shareRun(e,{shareId:t}={}){let r={run_id:e,share_token:t||a.A()};m(e);let i=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/runs/${e}/share`,{method:"PUT",headers:this.headers,body:JSON.stringify(r),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),n=await i.json();if(null===n||!("share_token"in n))throw Error("Invalid response from server");return`${this.getHostUrl()}/public/${n.share_token}/r`}async unshareRun(e){m(e);let t=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/runs/${e}/share`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(t,"unshare run",!0)}async readRunSharedLink(e){m(e);let t=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/runs/${e}/share`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),r=await t.json();if(null!==r&&"share_token"in r)return`${this.getHostUrl()}/public/${r.share_token}/r`}async listSharedRuns(e,{runIds:t}={}){let r=new URLSearchParams({share_token:e});if(void 0!==t)for(let e of t)r.append("id",e);m(e);let a=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/public/${e}/runs${r}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await a.json()}async readDatasetSharedSchema(e,t){if(!e&&!t)throw Error("Either datasetId or datasetName must be given");e||(e=(await this.readDataset({datasetName:t})).id),m(e);let r=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/datasets/${e}/share`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),a=await r.json();return a.url=`${this.getHostUrl()}/public/${a.share_token}/d`,a}async shareDataset(e,t){if(!e&&!t)throw Error("Either datasetId or datasetName must be given");e||(e=(await this.readDataset({datasetName:t})).id);let r={dataset_id:e};m(e);let a=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/datasets/${e}/share`,{method:"PUT",headers:this.headers,body:JSON.stringify(r),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),i=await a.json();return i.url=`${this.getHostUrl()}/public/${i.share_token}/d`,i}async unshareDataset(e){m(e);let t=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/datasets/${e}/share`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(t,"unshare dataset",!0)}async readSharedDataset(e){m(e);let t=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/public/${e}/datasets`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await t.json()}async listSharedExamples(e,t){let r={};t?.exampleIds&&(r.id=t.exampleIds);let a=new URLSearchParams;Object.entries(r).forEach(([e,t])=>{Array.isArray(t)?t.forEach(t=>a.append(e,t)):a.append(e,t)});let i=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/public/${e}/examples?${a.toString()}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),n=await i.json();if(!i.ok){if("detail"in n)throw Error(`Failed to list shared examples.
Status: ${i.status}
Message: ${Array.isArray(n.detail)?n.detail.join("\n"):"Unspecified error"}`);throw Error(`Failed to list shared examples: ${i.status} ${i.statusText}`)}return n.map(e=>({...e,_hostUrl:this.getHostUrl()}))}async createProject({projectName:e,description:t=null,metadata:r=null,upsert:a=!1,projectExtra:i=null,referenceDatasetId:n=null}){let o=`${this.apiUrl}/sessions${a?"?upsert=true":""}`,l=i||{};r&&(l.metadata=r);let u={name:e,extra:l,description:t};null!==n&&(u.reference_dataset_id=n);let c=await this.caller.call((0,s.Yx)(this.debug),o,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(u),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(c,"create project"),await c.json()}async updateProject(e,{name:t=null,description:r=null,metadata:a=null,projectExtra:i=null,endTime:n=null}){let o=`${this.apiUrl}/sessions/${e}`,l=i;a&&(l={...l||{},metadata:a});let u={name:t,extra:l,description:r,end_time:n?new Date(n).toISOString():null},c=await this.caller.call((0,s.Yx)(this.debug),o,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(u),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(c,"update project"),await c.json()}async hasProject({projectId:e,projectName:t}){let r="/sessions",a=new URLSearchParams;if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");if(void 0!==e)m(e),r+=`/${e}`;else if(void 0!==t)a.append("name",t);else throw Error("Must provide projectName or projectId");let i=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}${r}?${a}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});try{let e=await i.json();if(!i.ok)return!1;if(Array.isArray(e))return e.length>0;return!0}catch(e){return!1}}async readProject({projectId:e,projectName:t,includeStats:r}){let a,i="/sessions",n=new URLSearchParams;if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");if(void 0!==e)m(e),i+=`/${e}`;else if(void 0!==t)n.append("name",t);else throw Error("Must provide projectName or projectId");void 0!==r&&n.append("include_stats",r.toString());let s=await this._get(i,n);if(Array.isArray(s)){if(0===s.length)throw Error(`Project[id=${e}, name=${t}] not found`);a=s[0]}else a=s;return a}async getProjectUrl({projectId:e,projectName:t}){if(void 0===e&&void 0===t)throw Error("Must provide either projectName or projectId");let r=await this.readProject({projectId:e,projectName:t}),a=await this._getTenantId();return`${this.getHostUrl()}/o/${a}/projects/p/${r.id}`}async getDatasetUrl({datasetId:e,datasetName:t}){if(void 0===e&&void 0===t)throw Error("Must provide either datasetName or datasetId");let r=await this.readDataset({datasetId:e,datasetName:t}),a=await this._getTenantId();return`${this.getHostUrl()}/o/${a}/datasets/${r.id}`}async _getTenantId(){if(null!==this._tenantId)return this._tenantId;let e=new URLSearchParams({limit:"1"});for await(let t of this._getPaginated("/sessions",e))return this._tenantId=t[0].tenant_id,t[0].tenant_id;throw Error("No projects found to resolve tenant.")}async *listProjects({projectIds:e,name:t,nameContains:r,referenceDatasetId:a,referenceDatasetName:i,referenceFree:n,metadata:s}={}){let o=new URLSearchParams;if(void 0!==e)for(let t of e)o.append("id",t);if(void 0!==t&&o.append("name",t),void 0!==r&&o.append("name_contains",r),void 0!==a)o.append("reference_dataset",a);else if(void 0!==i){let e=await this.readDataset({datasetName:i});o.append("reference_dataset",e.id)}for await(let e of(void 0!==n&&o.append("reference_free",n.toString()),void 0!==s&&o.append("metadata",JSON.stringify(s)),this._getPaginated("/sessions",o)))yield*e}async deleteProject({projectId:e,projectName:t}){let r;if(void 0===e&&void 0===t)throw Error("Must provide projectName or projectId");if(void 0!==e&&void 0!==t)throw Error("Must provide either projectName or projectId, not both");m(r=void 0===e?(await this.readProject({projectName:t})).id:e);let a=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/sessions/${r}`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(a,`delete session ${r} (${t})`,!0)}async uploadCsv({csvFile:e,fileName:t,inputKeys:r,outputKeys:a,description:i,dataType:n,name:o}){let l=`${this.apiUrl}/datasets/upload`,u=new FormData;u.append("file",e,t),r.forEach(e=>{u.append("input_keys",e)}),a.forEach(e=>{u.append("output_keys",e)}),i&&u.append("description",i),n&&u.append("data_type",n),o&&u.append("name",o);let c=await this.caller.call((0,s.Yx)(this.debug),l,{method:"POST",headers:this.headers,body:u,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(c,"upload CSV"),await c.json()}async createDataset(e,{description:t,dataType:r,inputsSchema:a,outputsSchema:i,metadata:n}={}){let o={name:e,description:t,extra:n?{metadata:n}:void 0};r&&(o.data_type=r),a&&(o.inputs_schema_definition=a),i&&(o.outputs_schema_definition=i);let l=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/datasets`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(o),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(l,"create dataset"),await l.json()}async readDataset({datasetId:e,datasetName:t}){let r,a="/datasets",i=new URLSearchParams({limit:"1"});if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==e)m(e),a+=`/${e}`;else if(void 0!==t)i.append("name",t);else throw Error("Must provide datasetName or datasetId");let n=await this._get(a,i);if(Array.isArray(n)){if(0===n.length)throw Error(`Dataset[id=${e}, name=${t}] not found`);r=n[0]}else r=n;return r}async hasDataset({datasetId:e,datasetName:t}){try{return await this.readDataset({datasetId:e,datasetName:t}),!0}catch(e){if(e instanceof Error&&e.message.toLocaleLowerCase().includes("not found"))return!1;throw e}}async diffDatasetVersions({datasetId:e,datasetName:t,fromVersion:r,toVersion:a}){let i=e;if(void 0===i&&void 0===t)throw Error("Must provide either datasetName or datasetId");if(void 0!==i&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");void 0===i&&(i=(await this.readDataset({datasetName:t})).id);let n=new URLSearchParams({from_version:"string"==typeof r?r:r.toISOString(),to_version:"string"==typeof a?a:a.toISOString()});return await this._get(`/datasets/${i}/versions/diff`,n)}async readDatasetOpenaiFinetuning({datasetId:e,datasetName:t}){if(void 0!==e);else if(void 0!==t)e=(await this.readDataset({datasetName:t})).id;else throw Error("Must provide either datasetName or datasetId");let r=await this._getResponse(`/datasets/${e}/openai_ft`);return(await r.text()).trim().split("\n").map(e=>JSON.parse(e))}async *listDatasets({limit:e=100,offset:t=0,datasetIds:r,datasetName:a,datasetNameContains:i,metadata:n}={}){let s=new URLSearchParams({limit:e.toString(),offset:t.toString()});if(void 0!==r)for(let e of r)s.append("id",e);for await(let e of(void 0!==a&&s.append("name",a),void 0!==i&&s.append("name_contains",i),void 0!==n&&s.append("metadata",JSON.stringify(n)),this._getPaginated("/datasets",s)))yield*e}async updateDataset(e){let{datasetId:t,datasetName:r,...a}=e;if(!t&&!r)throw Error("Must provide either datasetName or datasetId");let i=t??(await this.readDataset({datasetName:r})).id;m(i);let n=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/datasets/${i}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(a),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(n,"update dataset"),await n.json()}async updateDatasetTag(e){let{datasetId:t,datasetName:r,asOf:a,tag:i}=e;if(!t&&!r)throw Error("Must provide either datasetName or datasetId");let n=t??(await this.readDataset({datasetName:r})).id;m(n);let o=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/datasets/${n}/tags`,{method:"PUT",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify({as_of:"string"==typeof a?a:a.toISOString(),tag:i}),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(o,"update dataset tags")}async deleteDataset({datasetId:e,datasetName:t}){let r="/datasets",a=e;if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==t&&(a=(await this.readDataset({datasetName:t})).id),void 0!==a)m(a),r+=`/${a}`;else throw Error("Must provide datasetName or datasetId");let i=await this.caller.call((0,s.Yx)(this.debug),this.apiUrl+r,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(i,`delete ${r}`),await i.json()}async indexDataset({datasetId:e,datasetName:t,tag:r}){let a=e;if(a||t)if(a&&t)throw Error("Must provide either datasetName or datasetId, not both");else a||(a=(await this.readDataset({datasetName:t})).id);else throw Error("Must provide either datasetName or datasetId");m(a);let i=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/datasets/${a}/index`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify({tag:r}),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(i,"index dataset"),await i.json()}async similarExamples(e,t,r,{filter:a}={}){let i={limit:r,inputs:e};void 0!==a&&(i.filter=a),m(t);let n=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/datasets/${t}/search`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(i),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(n,"fetch similar examples"),(await n.json()).examples}async createExample(e,t,r){let i;if(L(e)&&(void 0!==t||void 0!==r))throw Error("Cannot provide outputs or options when using ExampleCreate object");let n=t?r?.datasetId:e.dataset_id,s=t?r?.datasetName:e.dataset_name;if(void 0===n&&void 0===s)throw Error("Must provide either datasetName or datasetId");if(void 0!==n&&void 0!==s)throw Error("Must provide either datasetName or datasetId, not both");void 0===n&&(n=(await this.readDataset({datasetName:s})).id);let o=(t?r?.createdAt:e.created_at)||new Date;i=L(e)?e:{inputs:e,outputs:t,created_at:o?.toISOString(),id:r?.exampleId,metadata:r?.metadata,split:r?.split,source_run_id:r?.sourceRunId,use_source_run_io:r?.useSourceRunIO,use_source_run_attachments:r?.useSourceRunAttachments,attachments:r?.attachments};let l=await this._uploadExamplesMultipart(n,[i]);return await this.readExample(l.example_ids?.[0]??a.A())}async createExamples(e){if(Array.isArray(e)){if(0===e.length)return[];let t=e[0].dataset_id,r=e[0].dataset_name;if(void 0===t&&void 0===r)throw Error("Must provide either datasetName or datasetId");if(void 0!==t&&void 0!==r)throw Error("Must provide either datasetName or datasetId, not both");void 0===t&&(t=(await this.readDataset({datasetName:r})).id);let a=await this._uploadExamplesMultipart(t,e);return await Promise.all(a.example_ids.map(e=>this.readExample(e)))}let{inputs:t,outputs:r,metadata:a,splits:i,sourceRunIds:n,useSourceRunIOs:s,useSourceRunAttachments:o,attachments:l,exampleIds:u,datasetId:c,datasetName:d}=e;if(void 0===t)throw Error("Must provide inputs when using legacy parameters");let h=c;if(void 0===h&&void 0===d)throw Error("Must provide either datasetName or datasetId");if(void 0!==h&&void 0!==d)throw Error("Must provide either datasetName or datasetId, not both");void 0===h&&(h=(await this.readDataset({datasetName:d})).id);let p=t.map((e,t)=>({dataset_id:h,inputs:e,outputs:r?.[t],metadata:a?.[t],split:i?.[t],id:u?.[t],attachments:l?.[t],source_run_id:n?.[t],use_source_run_io:s?.[t],use_source_run_attachments:o?.[t]})),f=await this._uploadExamplesMultipart(h,p);return await Promise.all(f.example_ids.map(e=>this.readExample(e)))}async createLLMExample(e,t,r){return this.createExample({input:e},{output:t},r)}async createChatExample(e,t,r){let a=e.map(e=>c(e)?d(e):e),i=c(t)?d(t):t;return this.createExample({input:a},{output:i},r)}async readExample(e){m(e);let t=`/examples/${e}`,{attachment_urls:r,...a}=await this._get(t);return r&&(a.attachments=Object.entries(r).reduce((e,[t,r])=>(e[t.slice(11)]={presigned_url:r.presigned_url,mime_type:r.mime_type},e),{})),a}async *listExamples({datasetId:e,datasetName:t,exampleIds:r,asOf:a,splits:i,inlineS3Urls:n,metadata:s,limit:o,offset:l,filter:u,includeAttachments:c}={}){let d;if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");if(void 0!==e)d=e;else if(void 0!==t)d=(await this.readDataset({datasetName:t})).id;else throw Error("Must provide a datasetName or datasetId");let h=new URLSearchParams({dataset:d}),p=a?"string"==typeof a?a:a?.toISOString():void 0;if(p&&h.append("as_of",p),h.append("inline_s3_urls",(n??!0).toString()),void 0!==r)for(let e of r)h.append("id",e);if(void 0!==i)for(let e of i)h.append("splits",e);if(void 0!==s){let e=JSON.stringify(s);h.append("metadata",e)}void 0!==o&&h.append("limit",o.toString()),void 0!==l&&h.append("offset",l.toString()),void 0!==u&&h.append("filter",u),!0===c&&["attachment_urls","outputs","metadata"].forEach(e=>h.append("select",e));let f=0;for await(let e of this._getPaginated("/examples",h)){for(let t of e){let{attachment_urls:e,...r}=t;e&&(r.attachments=Object.entries(e).reduce((e,[t,r])=>(e[t.slice(11)]={presigned_url:r.presigned_url,mime_type:r.mime_type||void 0},e),{})),yield r,f++}if(void 0!==o&&f>=o)break}}async deleteExample(e){m(e);let t=`/examples/${e}`,r=await this.caller.call((0,s.Yx)(this.debug),this.apiUrl+t,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(r,`delete ${t}`),await r.json()}async updateExample(e,t){let r,a,i;return m(r=t?e:e.id),i=void 0!==(a=t?{id:r,...t}:e).dataset_id?a.dataset_id:(await this.readExample(r)).dataset_id,this._updateExamplesMultipart(i,[a])}async updateExamples(e){let t;return t=void 0===e[0].dataset_id?(await this.readExample(e[0].id)).dataset_id:e[0].dataset_id,this._updateExamplesMultipart(t,e)}async readDatasetVersion({datasetId:e,datasetName:t,asOf:r,tag:a}){let i;if(m(i=e||(await this.readDataset({datasetName:t})).id),r&&a||!r&&!a)throw Error("Exactly one of asOf and tag must be specified.");let n=new URLSearchParams;void 0!==r&&n.append("as_of","string"==typeof r?r:r.toISOString()),void 0!==a&&n.append("tag",a);let o=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/datasets/${i}/version?${n.toString()}`,{method:"GET",headers:{...this.headers},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(o,"read dataset version"),await o.json()}async listDatasetSplits({datasetId:e,datasetName:t,asOf:r}){let a;if(void 0===e&&void 0===t)throw Error("Must provide dataset name or ID");if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");m(a=void 0===e?(await this.readDataset({datasetName:t})).id:e);let i=new URLSearchParams,n=r?"string"==typeof r?r:r?.toISOString():void 0;return n&&i.append("as_of",n),await this._get(`/datasets/${a}/splits`,i)}async updateDatasetSplits({datasetId:e,datasetName:t,splitName:r,exampleIds:a,remove:i=!1}){let n;if(void 0===e&&void 0===t)throw Error("Must provide dataset name or ID");if(void 0!==e&&void 0!==t)throw Error("Must provide either datasetName or datasetId, not both");m(n=void 0===e?(await this.readDataset({datasetName:t})).id:e);let o={split_name:r,examples:a.map(e=>(m(e),e)),remove:i},l=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/datasets/${n}/splits`,{method:"PUT",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(o),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(l,"update dataset splits",!0)}async evaluateRun(e,t,{sourceInfo:r,loadChildRuns:a,referenceExample:i}={loadChildRuns:!1}){let n;if((0,g.m)("This method is deprecated and will be removed in future LangSmith versions, use `evaluate` from `langsmith/evaluation` instead."),"string"==typeof e)n=await this.readRun(e,{loadChildRuns:a});else if("object"==typeof e&&"id"in e)n=e;else throw Error(`Invalid run type: ${typeof e}`);null!==n.reference_example_id&&void 0!==n.reference_example_id&&(i=await this.readExample(n.reference_example_id));let s=await t.evaluateRun(n,i),[o,l]=await this._logEvaluationFeedback(s,n,r);return l[0]}async createFeedback(e,t,{score:r,value:i,correction:n,comment:o,sourceInfo:l,feedbackSourceType:u="api",sourceRunId:c,feedbackId:d,feedbackConfig:h,projectId:p,comparativeExperimentId:f}){if(!e&&!p)throw Error("One of runId or projectId must be provided");if(e&&p)throw Error("Only one of runId or projectId can be provided");let g={type:u??"api",metadata:l??{}};void 0===c||g?.metadata===void 0||g.metadata.__run||(g.metadata.__run={run_id:c}),g?.metadata!==void 0&&g.metadata.__run?.run_id!==void 0&&m(g.metadata.__run.run_id);let y={id:d??a.A(),run_id:e,key:t,score:R(r),value:i,correction:n,comment:o,feedback_source:g,comparative_experiment_id:f,feedbackConfig:h,session_id:p},b=`${this.apiUrl}/feedback`,v=await this.caller.call((0,s.Yx)(this.debug),b,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(y),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(v,"create feedback",!0),y}async updateFeedback(e,{score:t,value:r,correction:a,comment:i}){let n={};null!=t&&(n.score=R(t)),null!=r&&(n.value=r),null!=a&&(n.correction=a),null!=i&&(n.comment=i),m(e);let o=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/feedback/${e}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(n),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(o,"update feedback",!0)}async readFeedback(e){m(e);let t=`/feedback/${e}`;return await this._get(t)}async deleteFeedback(e){m(e);let t=`/feedback/${e}`,r=await this.caller.call((0,s.Yx)(this.debug),this.apiUrl+t,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(r,`delete ${t}`),await r.json()}async *listFeedback({runIds:e,feedbackKeys:t,feedbackSourceTypes:r}={}){let a=new URLSearchParams;if(e&&a.append("run",e.join(",")),t)for(let e of t)a.append("key",e);if(r)for(let e of r)a.append("source",e);for await(let e of this._getPaginated("/feedback",a))yield*e}async createPresignedFeedbackToken(e,t,{expiration:r,feedbackConfig:a}={}){let i={run_id:e,feedback_key:t,feedback_config:a};r?"string"==typeof r?i.expires_at=r:(r?.hours||r?.minutes||r?.days)&&(i.expires_in=r):i.expires_in={hours:3};let n=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/feedback/tokens`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(i),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await n.json()}async createComparativeExperiment({name:e,experimentIds:t,referenceDatasetId:r,createdAt:a,description:i,metadata:n,id:o}){if(0===t.length)throw Error("At least one experiment is required");if(r||(r=(await this.readProject({projectId:t[0]})).reference_dataset_id),null==!r)throw Error("A reference dataset is required");let l={id:o,name:e,experiment_ids:t,reference_dataset_id:r,description:i,created_at:(a??new Date)?.toISOString(),extra:{}};n&&(l.extra.metadata=n);let u=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/datasets/comparative`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(l),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await u.json()}async *listPresignedFeedbackTokens(e){m(e);let t=new URLSearchParams({run_id:e});for await(let e of this._getPaginated("/feedback/tokens",t))yield*e}_selectEvalResults(e){let t;return"results"in e?e.results:Array.isArray(e)?e:[e]}async _logEvaluationFeedback(e,t,r){let a=this._selectEvalResults(e),i=[];for(let e of a){let a=r||{};e.evaluatorInfo&&(a={...e.evaluatorInfo,...a});let n=null;e.targetRunId?n=e.targetRunId:t&&(n=t.id),i.push(await this.createFeedback(n,e.key,{score:e.score,value:e.value,comment:e.comment,correction:e.correction,sourceInfo:a,sourceRunId:e.sourceRunId,feedbackConfig:e.feedbackConfig,feedbackSourceType:"model"}))}return[a,i]}async logEvaluationFeedback(e,t,r){let[a]=await this._logEvaluationFeedback(e,t,r);return a}async *listAnnotationQueues(e={}){let{queueIds:t,name:r,nameContains:a,limit:i}=e,n=new URLSearchParams;t&&t.forEach((e,t)=>{m(e,`queueIds[${t}]`),n.append("ids",e)}),r&&n.append("name",r),a&&n.append("name_contains",a),n.append("limit",(void 0!==i?Math.min(i,100):100).toString());let s=0;for await(let e of this._getPaginated("/annotation-queues",n))if(yield*e,s++,void 0!==i&&s>=i)break}async createAnnotationQueue(e){let{name:t,description:r,queueId:i,rubricInstructions:n}=e,o={name:t,description:r,id:i||a.A(),rubric_instructions:n},l=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/annotation-queues`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(Object.fromEntries(Object.entries(o).filter(([e,t])=>void 0!==t))),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(l,"create annotation queue"),await l.json()}async readAnnotationQueue(e){let t=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/annotation-queues/${m(e,"queueId")}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(t,"read annotation queue"),await t.json()}async updateAnnotationQueue(e,t){let{name:r,description:a,rubricInstructions:i}=t,n=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/annotation-queues/${m(e,"queueId")}`,{method:"PATCH",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify({name:r,description:a,rubric_instructions:i}),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(n,"update annotation queue")}async deleteAnnotationQueue(e){let t=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/annotation-queues/${m(e,"queueId")}`,{method:"DELETE",headers:{...this.headers,Accept:"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(t,"delete annotation queue")}async addRunsToAnnotationQueue(e,t){let r=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/annotation-queues/${m(e,"queueId")}/runs`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(t.map((e,t)=>m(e,`runIds[${t}]`).toString())),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(r,"add runs to annotation queue")}async getRunFromAnnotationQueue(e,t){let r=`/annotation-queues/${m(e,"queueId")}/run`,a=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}${r}/${t}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(a,"get run from annotation queue"),await a.json()}async deleteRunFromAnnotationQueue(e,t){let r=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/annotation-queues/${m(e,"queueId")}/runs/${m(t,"queueRunId")}`,{method:"DELETE",headers:{...this.headers,Accept:"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(r,"delete run from annotation queue")}async getSizeFromAnnotationQueue(e){let t=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/annotation-queues/${m(e,"queueId")}/size`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(t,"get size from annotation queue"),await t.json()}async _currentTenantIsOwner(e){let t=await this._getSettings();return"-"==e||t.tenant_handle===e}async _ownerConflictError(e,t){let r=await this._getSettings();return Error(`Cannot ${e} for another tenant.

      Current tenant: ${r.tenant_handle}

      Requested tenant: ${t}`)}async _getLatestCommitHash(e){let t=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/commits/${e}/?limit=1&offset=0`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions}),r=await t.json();if(!t.ok){let e="string"==typeof r.detail?r.detail:JSON.stringify(r.detail),a=Error(`Error ${t.status}: ${t.statusText}
${e}`);throw a.statusCode=t.status,a}if(0!==r.commits.length)return r.commits[0].commit_hash}async _likeOrUnlikePrompt(e,t){let[r,a,i]=y(e),n=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/likes/${r}/${a}`,{method:"POST",body:JSON.stringify({like:t}),headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(n,`${t?"like":"unlike"} prompt`),await n.json()}async _getPromptUrl(e){let[t,r,a]=y(e);if(await this._currentTenantIsOwner(t)){let e=await this._getSettings();return"latest"!==a?`${this.getHostUrl()}/prompts/${r}/${a.substring(0,8)}?organizationId=${e.id}`:`${this.getHostUrl()}/prompts/${r}?organizationId=${e.id}`}return"latest"!==a?`${this.getHostUrl()}/hub/${t}/${r}/${a.substring(0,8)}`:`${this.getHostUrl()}/hub/${t}/${r}`}async promptExists(e){return!!await this.getPrompt(e)}async likePrompt(e){return this._likeOrUnlikePrompt(e,!0)}async unlikePrompt(e){return this._likeOrUnlikePrompt(e,!1)}async *listCommits(e){for await(let t of this._getPaginated(`/commits/${e}/`,new URLSearchParams,e=>e.commits))yield*t}async *listPrompts(e){let t=new URLSearchParams;for await(let r of(t.append("sort_field",e?.sortField??"updated_at"),t.append("sort_direction","desc"),t.append("is_archived",(!!e?.isArchived).toString()),e?.isPublic!==void 0&&t.append("is_public",e.isPublic.toString()),e?.query&&t.append("query",e.query),this._getPaginated("/repos",t,e=>e.repos)))yield*r}async getPrompt(e){let[t,r,a]=y(e),i=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/repos/${t}/${r}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});if(404===i.status)return null;await _(i,"get prompt");let n=await i.json();return n.repo?n.repo:null}async createPrompt(e,t){let r=await this._getSettings();if(t?.isPublic&&!r.tenant_handle)throw Error(`Cannot create a public prompt without first

        creating a LangChain Hub handle. 
        You can add a handle by creating a public prompt at:

        https://smith.langchain.com/prompts`);let[a,i,n]=y(e);if(!await this._currentTenantIsOwner(a))throw await this._ownerConflictError("create a prompt",a);let o={repo_handle:i,...t?.description&&{description:t.description},...t?.readme&&{readme:t.readme},...t?.tags&&{tags:t.tags},is_public:!!t?.isPublic},l=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/repos/`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(o),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(l,"create prompt");let{repo:u}=await l.json();return u}async createCommit(e,t,r){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[a,i,n]=y(e),o=r?.parentCommitHash!=="latest"&&r?.parentCommitHash?r?.parentCommitHash:await this._getLatestCommitHash(`${a}/${i}`),l={manifest:JSON.parse(JSON.stringify(t)),parent_commit:o},u=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/commits/${a}/${i}`,{method:"POST",headers:{...this.headers,"Content-Type":"application/json"},body:JSON.stringify(l),signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(u,"create commit");let c=await u.json();return this._getPromptUrl(`${a}/${i}${c.commit_hash?`:${c.commit_hash}`:""}`)}async updateExamplesMultipart(e,t=[]){return this._updateExamplesMultipart(e,t)}async _updateExamplesMultipart(e,t=[]){if(!await this._getMultiPartSupport())throw Error("Your LangSmith deployment does not allow using the multipart examples endpoint, please upgrade your deployment to the latest version.");let r=new FormData;for(let e of t){let t=e.id,a=new Blob([I({...e.metadata&&{metadata:e.metadata},...e.split&&{split:e.split}},`Serializing body for example with id: ${t}`)],{type:"application/json"});if(r.append(t,a),e.inputs){let a=new Blob([I(e.inputs,`Serializing inputs for example with id: ${t}`)],{type:"application/json"});r.append(`${t}.inputs`,a)}if(e.outputs){let a=new Blob([I(e.outputs,`Serializing outputs whle updating example with id: ${t}`)],{type:"application/json"});r.append(`${t}.outputs`,a)}if(e.attachments)for(let[a,i]of Object.entries(e.attachments)){let e,n;Array.isArray(i)?[e,n]=i:(e=i.mimeType,n=i.data);let s=new Blob([n],{type:`${e}; length=${n.byteLength}`});r.append(`${t}.attachment.${a}`,s)}if(e.attachments_operations){let a=new Blob([I(e.attachments_operations,`Serializing attachments while updating example with id: ${t}`)],{type:"application/json"});r.append(`${t}.attachments_operations`,a)}}let a=e??t[0]?.dataset_id,i=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/v1/platform/datasets/${a}/examples`,{method:"PATCH",headers:this.headers,body:r});return await i.json()}async uploadExamplesMultipart(e,t=[]){return this._uploadExamplesMultipart(e,t)}async _uploadExamplesMultipart(e,t=[]){if(!await this._getMultiPartSupport())throw Error("Your LangSmith deployment does not allow using the multipart examples endpoint, please upgrade your deployment to the latest version.");let r=new FormData;for(let e of t){let t=(e.id??a.A()).toString(),i=new Blob([I({created_at:e.created_at,...e.metadata&&{metadata:e.metadata},...e.split&&{split:e.split},...e.source_run_id&&{source_run_id:e.source_run_id},...e.use_source_run_io&&{use_source_run_io:e.use_source_run_io},...e.use_source_run_attachments&&{use_source_run_attachments:e.use_source_run_attachments}},`Serializing body for uploaded example with id: ${t}`)],{type:"application/json"});if(r.append(t,i),e.inputs){let a=new Blob([I(e.inputs,`Serializing inputs for uploaded example with id: ${t}`)],{type:"application/json"});r.append(`${t}.inputs`,a)}if(e.outputs){let a=new Blob([I(e.outputs,`Serializing outputs for uploaded example with id: ${t}`)],{type:"application/json"});r.append(`${t}.outputs`,a)}if(e.attachments)for(let[a,i]of Object.entries(e.attachments)){let e,n;Array.isArray(i)?[e,n]=i:(e=i.mimeType,n=i.data);let s=new Blob([n],{type:`${e}; length=${n.byteLength}`});r.append(`${t}.attachment.${a}`,s)}}let i=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/v1/platform/datasets/${e}/examples`,{method:"POST",headers:this.headers,body:r});return await _(i,"upload examples"),await i.json()}async updatePrompt(e,t){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[r,a]=y(e);if(!await this._currentTenantIsOwner(r))throw await this._ownerConflictError("update a prompt",r);let i={};if(t?.description!==void 0&&(i.description=t.description),t?.readme!==void 0&&(i.readme=t.readme),t?.tags!==void 0&&(i.tags=t.tags),t?.isPublic!==void 0&&(i.is_public=t.isPublic),t?.isArchived!==void 0&&(i.is_archived=t.isArchived),0===Object.keys(i).length)throw Error("No valid update options provided");let n=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/repos/${r}/${a}`,{method:"PATCH",body:JSON.stringify(i),headers:{...this.headers,"Content-Type":"application/json"},signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await _(n,"update prompt"),n.json()}async deletePrompt(e){if(!await this.promptExists(e))throw Error("Prompt does not exist, you must create it first.");let[t,r,a]=y(e);if(!await this._currentTenantIsOwner(t))throw await this._ownerConflictError("delete a prompt",t);let i=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/repos/${t}/${r}`,{method:"DELETE",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});return await i.json()}async pullPromptCommit(e,t){let[r,a,i]=y(e),n=await this.caller.call((0,s.Yx)(this.debug),`${this.apiUrl}/commits/${r}/${a}/${i}${t?.includeModel?"?include_model=true":""}`,{method:"GET",headers:this.headers,signal:AbortSignal.timeout(this.timeout_ms),...this.fetchOptions});await _(n,"pull prompt commit");let o=await n.json();return{owner:r,repo:a,commit_hash:o.commit_hash,manifest:o.manifest,examples:o.examples}}async _pullPrompt(e,t){return JSON.stringify((await this.pullPromptCommit(e,{includeModel:t?.includeModel})).manifest)}async pushPrompt(e,t){return(await this.promptExists(e)?t&&Object.keys(t).some(e=>"object"!==e)&&await this.updatePrompt(e,{description:t?.description,readme:t?.readme,tags:t?.tags,isPublic:t?.isPublic}):await this.createPrompt(e,{description:t?.description,readme:t?.readme,tags:t?.tags,isPublic:t?.isPublic}),t?.object)?await this.createCommit(e,t?.object,{parentCommitHash:t?.parentCommitHash}):await this._getPromptUrl(e)}async clonePublicDataset(e,t={}){let{sourceApiUrl:r=this.apiUrl,datasetName:a}=t,[i,n]=this.parseTokenOrUrl(e,r),s=new C({apiUrl:i,apiKey:"placeholder"}),o=await s.readSharedDataset(n),l=a||o.name;try{if(await this.hasDataset({datasetId:l}))return void console.log(`Dataset ${l} already exists in your tenant. Skipping.`)}catch(e){}let u=await s.listSharedExamples(n),c=await this.createDataset(l,{description:o.description,dataType:o.data_type||"kv",inputsSchema:o.inputs_schema_definition??void 0,outputsSchema:o.outputs_schema_definition??void 0});try{await this.createExamples({inputs:u.map(e=>e.inputs),outputs:u.flatMap(e=>e.outputs?[e.outputs]:[]),datasetId:c.id})}catch(e){throw console.error(`An error occurred while creating dataset ${l}. You should delete it manually.`),e}}parseTokenOrUrl(e,t,r=2,a="dataset"){try{return m(e),[t,e]}catch(e){}try{let i=new URL(e).pathname.split("/").filter(e=>""!==e);if(i.length>=r){let e=i[i.length-r];return[t,e]}throw Error(`Invalid public ${a} URL: ${e}`)}catch(t){throw Error(`Invalid public ${a} URL or token: ${e}`)}}awaitPendingTraceBatches(){return this.manualFlushMode?(console.warn("[WARNING]: When tracing in manual flush mode, you must call `await client.flush()` manually to submit trace batches."),Promise.resolve()):Promise.all([...this.autoBatchQueue.items.map(({itemPromise:e})=>e),this.batchIngestCaller.queue.onIdle()])}}function L(e){return"dataset_id"in e||"dataset_name"in e}},71505:(e,t,r)=>{"use strict";let a=r(3706);e.exports=(e,t,r)=>(e=new a(e,r),t=new a(t,r),e.intersects(t,r))},71611:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let a=0,i=e.length;for(;i>0;){let n=i/2|0,s=a+n;0>=r(e[s],t)?(a=++s,i-=n+1):i=n}return a}},71719:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let a=r(339),i=r(62502),n=r(12441),s=()=>{},o=new i.TimeoutError;class l extends a{constructor(e){var t,r,a,i;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=s,this._resolveIdle=s,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:n.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(r=null==(t=e.intervalCap)?void 0:t.toString())?r:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(i=null==(a=e.interval)?void 0:a.toString())?i:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=s,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=s,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,t={}){return new Promise((r,a)=>{let n=async()=>{this._pendingCount++,this._intervalCount++;try{let n=void 0===this._timeout&&void 0===t.timeout?e():i.default(Promise.resolve(e()),void 0===t.timeout?this._timeout:t.timeout,()=>{(void 0===t.throwOnTimeout?this._throwOnTimeout:t.throwOnTimeout)&&a(o)});r(await n)}catch(e){a(e)}this._next()};this._queue.enqueue(n,t),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}t.default=l},72180:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AsyncGeneratorWithSetup:()=>l,IterableReadableStream:()=>s,atee:()=>o,concat:()=>function e(t,r){if(Array.isArray(t)&&Array.isArray(r))return t.concat(r);if("string"==typeof t&&"string"==typeof r)return t+r;if("number"==typeof t&&"number"==typeof r)return t+r;if("concat"in t&&"function"==typeof t.concat)return t.concat(r);if("object"==typeof t&&"object"==typeof r){let a={...t};for(let[t,i]of Object.entries(r))t in a&&!Array.isArray(a[t])?a[t]=e(a[t],i):a[t]=i;return a}else throw Error(`Cannot concat ${typeof t} and ${typeof r}`)},pipeGeneratorWithSetup:()=>u});var a=r(27935),i=r(22123),n=r(28604);class s extends ReadableStream{constructor(){super(...arguments),Object.defineProperty(this,"reader",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}ensureReader(){this.reader||(this.reader=this.getReader())}async next(){this.ensureReader();try{let e=await this.reader.read();if(e.done)return this.reader.releaseLock(),{done:!0,value:void 0};return{done:!1,value:e.value}}catch(e){throw this.reader.releaseLock(),e}}async return(){if(this.ensureReader(),this.locked){let e=this.reader.cancel();this.reader.releaseLock(),await e}return{done:!0,value:void 0}}async throw(e){if(this.ensureReader(),this.locked){let e=this.reader.cancel();this.reader.releaseLock(),await e}throw e}[Symbol.asyncIterator](){return this}async [Symbol.asyncDispose](){await this.return()}static fromReadableStream(e){let t=e.getReader();return new s({start:e=>(function r(){return t.read().then(({done:t,value:a})=>t?void e.close():(e.enqueue(a),r()))})(),cancel(){t.releaseLock()}})}static fromAsyncGenerator(e){return new s({async pull(t){let{value:r,done:a}=await e.next();a&&t.close(),t.enqueue(r)},async cancel(t){await e.return(t)}})}}function o(e,t=2){let r=Array.from({length:t},()=>[]);return r.map(async function*(t){for(;;)if(0===t.length){let t=await e.next();for(let e of r)e.push(t)}else{if(t[0].done)return;yield t.shift().value}})}class l{constructor(e){Object.defineProperty(this,"generator",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"setup",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"signal",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"firstResult",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"firstResultUsed",{enumerable:!0,configurable:!0,writable:!0,value:!1}),this.generator=e.generator,this.config=e.config,this.signal=e.signal??this.config?.signal,this.setup=new Promise((t,r)=>{i.Nx.runWithConfig((0,a.DY)(e.config),async()=>{this.firstResult=e.generator.next(),e.startSetup?this.firstResult.then(e.startSetup).then(t,r):this.firstResult.then(e=>t(void 0),r)},!0)})}async next(...e){return(this.signal?.throwIfAborted(),this.firstResultUsed)?i.Nx.runWithConfig((0,a.DY)(this.config),this.signal?async()=>(0,n.o)(this.generator.next(...e),this.signal):async()=>this.generator.next(...e),!0):(this.firstResultUsed=!0,this.firstResult)}async return(e){return this.generator.return(e)}async throw(e){return this.generator.throw(e)}[Symbol.asyncIterator](){return this}async [Symbol.asyncDispose](){await this.return()}}async function u(e,t,r,a,...i){let n=new l({generator:t,startSetup:r,signal:a}),s=await n.setup;return{output:e(n,s,...i),setup:s}}},72892:(e,t,r)=>{"use strict";r.d(t,{AJ:()=>m,Ao:()=>l,F7:()=>d,Iv:()=>s,Vt:()=>c,XQ:()=>o,_I:()=>n,dp:()=>p,gj:()=>h,ns:()=>u,ny:()=>f});var a=r(84902),i=r(49517);function n(e,t){return"string"==typeof e?""===e?t:"string"==typeof t?e+t:Array.isArray(t)&&t.some(e=>(0,i.Fz)(e))?[{type:"text",source_type:"text",text:e},...t]:[{type:"text",text:e},...t]:Array.isArray(t)?c(e,t)??[...e,...t]:""===t?e:Array.isArray(e)&&e.some(e=>(0,i.Fz)(e))?[...e,{type:"file",source_type:"text",text:t}]:[...e,{type:"text",text:t}]}function s(e,t){return"error"===e||"error"===t?"error":"success"}class o extends a.Serializable{get lc_aliases(){return{additional_kwargs:"additional_kwargs",response_metadata:"response_metadata"}}get text(){return"string"==typeof this.content?this.content:Array.isArray(this.content)?this.content.map(e=>"string"==typeof e?e:"text"===e.type?e.text:"").join(""):""}getType(){return this._getType()}constructor(e,t){"string"==typeof e&&(e={content:e,additional_kwargs:t,response_metadata:{}}),e.additional_kwargs||(e.additional_kwargs={}),e.response_metadata||(e.response_metadata={}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","messages"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"content",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"additional_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"response_metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.content=e.content,this.additional_kwargs=e.additional_kwargs,this.response_metadata=e.response_metadata,this.id=e.id}toDict(){return{type:this._getType(),data:this.toJSON().kwargs}}static lc_name(){return"BaseMessage"}get _printableFields(){return{id:this.id,content:this.content,name:this.name,additional_kwargs:this.additional_kwargs,response_metadata:this.response_metadata}}_updateId(e){this.id=e,this.lc_kwargs.id=e}get[Symbol.toStringTag](){return this.constructor.lc_name()}[Symbol.for("nodejs.util.inspect.custom")](e){var t,r;if(null===e)return this;let a=(t=this._printableFields,r=Math.max(4,e),JSON.stringify(function e(t,a){if("object"!=typeof t||null==t)return t;if(a>=r)return Array.isArray(t)?"[Array]":"[Object]";if(Array.isArray(t))return t.map(t=>e(t,a+1));let i={};for(let r of Object.keys(t))i[r]=e(t[r],a+1);return i}(t,0),null,2));return`${this.constructor.lc_name()} ${a}`}}function l(e){return Array.isArray(e)&&e.every(e=>"number"==typeof e.index)}function u(e,t){let r={...e};for(let[e,a]of Object.entries(t))if(null==r[e])r[e]=a;else if(null==a)continue;else if(typeof r[e]!=typeof a||Array.isArray(r[e])!==Array.isArray(a))throw Error(`field[${e}] already exists in the message chunk, but with a different type.`);else if("string"==typeof r[e]){if("type"===e)continue;r[e]+=a}else if("object"!=typeof r[e]||Array.isArray(r[e]))if(Array.isArray(r[e]))r[e]=c(r[e],a);else{if(r[e]===a)continue;console.warn(`field[${e}] already exists in this message chunk and value has unsupported type.`)}else r[e]=u(r[e],a);return r}function c(e,t){if(void 0!==e||void 0!==t){if(void 0===e||void 0===t)return e||t;let r=[...e];for(let e of t)if("object"==typeof e&&"index"in e&&"number"==typeof e.index){let t=r.findIndex(t=>t.index===e.index);-1!==t?r[t]=u(r[t],e):r.push(e)}else{if("object"==typeof e&&"text"in e&&""===e.text)continue;r.push(e)}return r}}function d(e,t){if(!e&&!t)throw Error("Cannot merge two undefined objects.");if(!e||!t)return e||t;if(typeof e!=typeof t)throw Error(`Cannot merge objects of different types.
Left ${typeof e}
Right ${typeof t}`);if("string"==typeof e&&"string"==typeof t)return e+t;if(Array.isArray(e)&&Array.isArray(t))return c(e,t);if("object"==typeof e&&"object"==typeof t)return u(e,t);else if(e===t)return e;else throw Error(`Can not merge objects of different types.
Left ${e}
Right ${t}`)}class h extends o{}function p(e){return"string"==typeof e.role}function f(e){return"function"==typeof e?._getType}function m(e){return f(e)&&"function"==typeof e.concat}},73051:(e,t,r)=>{"use strict";let a=r(58361);e.exports=(e,t)=>{let r=a(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}},73438:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>0===a(e,t,r)},73726:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let a=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i,i=function(e){return"string"==typeof e&&a.test(e)}},74081:(e,t,r)=>{"use strict";r.d(t,{m:()=>i});let a={};function i(e){a[e]||(console.warn(e),a[e]=!0)}},75448:(e,t,r)=>{"use strict";r.d(t,{GZ:()=>o,a7:()=>n,di:()=>s,xc:()=>i});var a=r(72892);class i extends a.XQ{static lc_name(){return"HumanMessage"}_getType(){return"human"}constructor(e,t){super(e,t)}}class n extends a.gj{static lc_name(){return"HumanMessageChunk"}_getType(){return"human"}constructor(e,t){super(e,t)}concat(e){return new n({content:(0,a._I)(this.content,e.content),additional_kwargs:(0,a.ns)(this.additional_kwargs,e.additional_kwargs),response_metadata:(0,a.ns)(this.response_metadata,e.response_metadata),id:this.id??e.id})}}function s(e){return"human"===e.getType()}function o(e){return"human"===e.getType()}},76242:(e,t,r)=>{"use strict";r.r(t),r.d(t,{LangChainTracer:()=>l});var a=r(91962),i=r(65109),n=r(37664),s=r(65164),o=r(35638);class l extends s.BaseTracer{constructor(e={}){super(e),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"langchain_tracer"}),Object.defineProperty(this,"projectName",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"exampleId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"client",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"replicas",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"usesRunTreeMap",{enumerable:!0,configurable:!0,writable:!0,value:!0});let{exampleId:t,projectName:r,client:i,replicas:n}=e;this.projectName=r??(0,a.q7)(),this.replicas=n,this.exampleId=t,this.client=i??(0,o.h)();let s=l.getTraceableRunTree();s&&this.updateFromRunTree(s)}async persistRun(e){}async onRunCreate(e){let t=this.getRunTreeWithTracingConfig(e.id);await t?.postRun()}async onRunUpdate(e){let t=this.getRunTreeWithTracingConfig(e.id);await t?.patchRun()}getRun(e){return this.runTreeMap.get(e)}updateFromRunTree(e){let t=e,r=new Set;for(;t.parent_run&&!r.has(t.id)&&(r.add(t.id),t.parent_run);){;t=t.parent_run}r.clear();let a=[t];for(;a.length>0;){let e=a.shift();!(!e||r.has(e.id))&&(r.add(e.id),this.runTreeMap.set(e.id,e),e.child_runs&&a.push(...e.child_runs))}this.client=e.client??this.client,this.replicas=e.replicas??this.replicas,this.projectName=e.project_name??this.projectName,this.exampleId=e.reference_example_id??this.exampleId}getRunTreeWithTracingConfig(e){let t=this.runTreeMap.get(e);if(t)return new i.gk({...t,client:this.client,project_name:this.projectName,replicas:this.replicas,reference_example_id:this.exampleId,tracingEnabled:!0})}static getTraceableRunTree(){try{return(0,n.vR)(!0)}catch{return}}}},76947:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BaseDocumentTransformer:()=>n,Document:()=>a.y,MappingDocumentTransformer:()=>s});var a=r(47948),i=r(90163);class n extends i.YN{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","documents","transformers"]})}invoke(e,t){return this.transformDocuments(e)}}class s extends n{async transformDocuments(e){let t=[];for(let r of e){let e=await this._transformDocument(r);t.push(e)}return t}}},77860:(e,t,r)=>{"use strict";let a=r(42679),i=r(33877);e.exports=(e,t,r)=>{let n=[],s=null,o=null,l=e.sort((e,t)=>i(e,t,r));for(let e of l)a(e,t,r)?(o=e,s||(s=e)):(o&&n.push([s,o]),o=null,s=null);s&&n.push([s,null]);let u=[];for(let[e,t]of n)e===t?u.push(e):t||e!==l[0]?t?e===l[0]?u.push(`<=${t}`):u.push(`${e} - ${t}`):u.push(`>=${e}`):u.push("*");let c=u.join(" || "),d="string"==typeof t.raw?t.raw:String(t);return c.length<d.length?c:t}},78172:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t)=>new a(e,t).patch},78668:e=>{"use strict";let t=/^[0-9]+$/,r=(e,r)=>{let a=t.test(e),i=t.test(r);return a&&i&&(e*=1,r*=1),e===r?0:a&&!i?-1:i&&!a?1:e<r?-1:1};e.exports={compareIdentifiers:r,rcompareIdentifiers:(e,t)=>r(t,e)}},81174:e=>{function t(e,t){"boolean"==typeof t&&(t={forever:t}),this._originalTimeouts=JSON.parse(JSON.stringify(e)),this._timeouts=e,this._options=t||{},this._maxRetryTime=t&&t.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}e.exports=t,t.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)},t.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null},t.prototype.retry=function(e){if(this._timeout&&clearTimeout(this._timeout),!e)return!1;var t=new Date().getTime();if(e&&t-this._operationStart>=this._maxRetryTime)return this._errors.push(e),this._errors.unshift(Error("RetryOperation timeout occurred")),!1;this._errors.push(e);var r=this._timeouts.shift();if(void 0===r)if(!this._cachedTimeouts)return!1;else this._errors.splice(0,this._errors.length-1),r=this._cachedTimeouts.slice(-1);var a=this;return this._timer=setTimeout(function(){a._attempts++,a._operationTimeoutCb&&(a._timeout=setTimeout(function(){a._operationTimeoutCb(a._attempts)},a._operationTimeout),a._options.unref&&a._timeout.unref()),a._fn(a._attempts)},r),this._options.unref&&this._timer.unref(),!0},t.prototype.attempt=function(e,t){this._fn=e,t&&(t.timeout&&(this._operationTimeout=t.timeout),t.cb&&(this._operationTimeoutCb=t.cb));var r=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){r._operationTimeoutCb()},r._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)},t.prototype.try=function(e){console.log("Using RetryOperation.try() is deprecated"),this.attempt(e)},t.prototype.start=function(e){console.log("Using RetryOperation.start() is deprecated"),this.attempt(e)},t.prototype.start=t.prototype.try,t.prototype.errors=function(){return this._errors},t.prototype.attempts=function(){return this._attempts},t.prototype.mainError=function(){if(0===this._errors.length)return null;for(var e={},t=null,r=0,a=0;a<this._errors.length;a++){var i=this._errors[a],n=i.message,s=(e[n]||0)+1;e[n]=s,s>=r&&(t=i,r=s)}return t}},81412:(e,t,r)=>{"use strict";r.d(t,{Js:()=>b,K0:()=>f,Pz:()=>g,Sw:()=>m,ih:()=>_,rf:()=>y});var a=r(13346),i=r(48457),n=r(16275),s=r(72892),o=r(28279),l=r(57161),u=r(75448),c=r(98488),d=r(28895);function h(e){return(0,i.Ky)(e)?e:"string"==typeof e.id&&"function"===e.type&&"object"==typeof e.function&&null!==e.function&&"arguments"in e.function&&"string"==typeof e.function.arguments&&"name"in e.function&&"string"==typeof e.function.name?{id:e.id,args:JSON.parse(e.function.arguments),name:e.function.name,type:"tool_call"}:e}function p(e){let t,r;if("object"==typeof e&&null!=e&&1===e.lc&&Array.isArray(e.id)&&null!=e.kwargs&&"object"==typeof e.kwargs){let a=e.id.at(-1);t="HumanMessage"===a||"HumanMessageChunk"===a?"user":"AIMessage"===a||"AIMessageChunk"===a?"assistant":"SystemMessage"===a||"SystemMessageChunk"===a?"system":"FunctionMessage"===a||"FunctionMessageChunk"===a?"function":"ToolMessage"===a||"ToolMessageChunk"===a?"tool":"unknown",r=e.kwargs}else{let{type:a,...i}=e;t=a,r=i}if("human"===t||"user"===t)return new u.xc(r);if("ai"===t||"assistant"===t){let{tool_calls:e,...t}=r;if(!Array.isArray(e))return new n.Od(r);let a=e.map(h);return new n.Od({...t,tool_calls:a})}if("system"===t)return new c.tn(r);if("developer"===t)return new c.tn({...r,additional_kwargs:{...r.additional_kwargs,__openai_role__:"developer"}});if("tool"===t&&"tool_call_id"in r)return new d.uf({...r,content:r.content,tool_call_id:r.tool_call_id,name:r.name});throw(0,a.Y)(Error(`Unable to coerce message from array: only human, AI, system, developer, or tool message coercion is currently supported.

Received: ${JSON.stringify(e,null,2)}`),"MESSAGE_COERCION_FAILURE")}function f(e){if("string"==typeof e)return new u.xc(e);if((0,s.ny)(e))return e;if(Array.isArray(e)){let[t,r]=e;return p({type:t,content:r})}if(!(0,s.dp)(e))return p(e);{let{role:t,...r}=e;return p({...r,type:t})}}function m(e,t="Human",r="AI"){let a=[];for(let i of e){let e;if("human"===i._getType())e=t;else if("ai"===i._getType())e=r;else if("system"===i._getType())e="System";else if("function"===i._getType())e="Function";else if("tool"===i._getType())e="Tool";else if("generic"===i._getType())e=i.role;else throw Error(`Got unsupported message type: ${i._getType()}`);let n=i.name?`${i.name}, `:"",s="string"==typeof i.content?i.content:JSON.stringify(i.content,null,2);a.push(`${e}: ${n}${s}`)}return a.join("\n")}function g(e){let t=void 0!==e.data?e:{type:e.type,data:{content:e.text,role:e.role,name:void 0,tool_call_id:void 0}};switch(t.type){case"human":return new u.xc(t.data);case"ai":return new n.Od(t.data);case"system":return new c.tn(t.data);case"function":if(void 0===t.data.name)throw Error("Name must be defined for function messages");return new l.mg(t.data);case"tool":if(void 0===t.data.tool_call_id)throw Error("Tool call ID must be defined for tool messages");return new d.uf(t.data);case"generic":if(void 0===t.data.role)throw Error("Role must be defined for chat messages");return new o.cM(t.data);default:throw Error(`Got unexpected type: ${t.type}`)}}function y(e){return e.map(g)}function b(e){return e.map(e=>e.toDict())}function _(e){let t=e._getType();if("human"===t)return new u.a7({...e});if("ai"===t){let t={...e};return"tool_calls"in t&&(t={...t,tool_call_chunks:t.tool_calls?.map(e=>({...e,type:"tool_call_chunk",index:void 0,args:JSON.stringify(e.args)}))}),new n.H({...t})}if("system"===t)return new c.uU({...e});if("function"===t)return new l.FK({...e});if(o.cM.isInstance(e))return new o.XU({...e});else throw Error("Unknown message type.")}},82116:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(77598);let i={randomUUID:r.n(a)().randomUUID};var n=r(5286),s=r(89386);let o=function(e,t,r){if(i.randomUUID&&!t&&!e)return i.randomUUID();let a=(e=e||{}).random||(e.rng||n.A)();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=a[e];return t}return(0,s.k)(a)}},82506:(e,t,r)=>{"use strict";r.d(t,{T:()=>c});var a=r(73726),i=r(82116),n=r(87956);function s(e){return e.replace(/[^a-zA-Z-_0-9]/g,"_")}let o=["*","_","`"];async function l(e,t){let{backgroundColor:r="white"}=t??{},a=btoa(e);void 0!==r&&(/^#(?:[0-9a-fA-F]{3}){1,2}$/.test(r)||(r=`!${r}`));let i=`https://mermaid.ink/img/${a}?bgColor=${r}`,n=await fetch(i);if(!n.ok)throw Error(`Failed to render the graph using the Mermaid.INK API.
Status code: ${n.status}
Status text: ${n.statusText}`);return await n.blob()}var u=r(21760);class c{constructor(e){Object.defineProperty(this,"nodes",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"edges",{enumerable:!0,configurable:!0,writable:!0,value:[]}),this.nodes=e?.nodes??this.nodes,this.edges=e?.edges??this.edges}toJSON(){let e={};return Object.values(this.nodes).forEach((t,r)=>{e[t.id]=(0,a.A)(t.id)?r:t.id}),{nodes:Object.values(this.nodes).map(t=>({id:e[t.id],...(0,n.T)(t.data)?{type:"runnable",data:{id:t.data.lc_id,name:t.data.getName()}}:{type:"schema",data:{...(0,u.toJsonSchema)(t.data.schema),title:t.data.name}}})),edges:this.edges.map(t=>{let r={source:e[t.source],target:e[t.target]};return void 0!==t.data&&(r.data=t.data),void 0!==t.conditional&&(r.conditional=t.conditional),r})}}addNode(e,t,r){if(void 0!==t&&void 0!==this.nodes[t])throw Error(`Node with id ${t} already exists`);let s=t??(0,i.A)(),o={id:s,data:e,name:function(e,t){if(void 0!==e&&!(0,a.A)(e))return e;if(!(0,n.T)(t))return t.name??"UnknownSchema";try{let e=t.getName();return e=e.startsWith("Runnable")?e.slice(8):e}catch(e){return t.getName()}}(t,e),metadata:r};return this.nodes[s]=o,o}removeNode(e){delete this.nodes[e.id],this.edges=this.edges.filter(t=>t.source!==e.id&&t.target!==e.id)}addEdge(e,t,r,a){if(void 0===this.nodes[e.id])throw Error(`Source node ${e.id} not in graph`);if(void 0===this.nodes[t.id])throw Error(`Target node ${t.id} not in graph`);let i={source:e.id,target:t.id,data:r,conditional:a};return this.edges.push(i),i}firstNode(){return d(this)}lastNode(){return h(this)}extend(e,t=""){let r=t;Object.values(e.nodes).map(e=>e.id).every(a.A)&&(r="");let i=e=>r?`${r}:${e}`:e;Object.entries(e.nodes).forEach(([e,t])=>{this.nodes[i(e)]={...t,id:i(e)}});let n=e.edges.map(e=>({...e,source:i(e.source),target:i(e.target)}));this.edges=[...this.edges,...n];let s=e.firstNode(),o=e.lastNode();return[s?{id:i(s.id),data:s.data}:void 0,o?{id:i(o.id),data:o.data}:void 0]}trimFirstNode(){let e=this.firstNode();e&&d(this,[e.id])&&this.removeNode(e)}trimLastNode(){let e=this.lastNode();e&&h(this,[e.id])&&this.removeNode(e)}reid(){let e=Object.fromEntries(Object.values(this.nodes).map(e=>[e.id,e.name])),t=new Map;Object.values(e).forEach(e=>{t.set(e,(t.get(e)||0)+1)});let r=r=>{let i=e[r];return(0,a.A)(r)&&1===t.get(i)?i:r};return new c({nodes:Object.fromEntries(Object.entries(this.nodes).map(([e,t])=>[r(e),{...t,id:r(e)}])),edges:this.edges.map(e=>({...e,source:r(e.source),target:r(e.target)}))})}drawMermaid(e){let{withStyles:t,curveStyle:r,nodeColors:a={default:"fill:#f2f0ff,line-height:1.2",first:"fill-opacity:0",last:"fill:#bfb6fc"},wrapLabelNWords:i}=e??{},n=this.reid(),l=n.firstNode(),u=n.lastNode();return function(e,t,r){let{firstNode:a,lastNode:i,nodeColors:n,withStyles:l=!0,curveStyle:u="linear",wrapLabelNWords:c=9}=r??{},d=l?`%%{init: {'flowchart': {'curve': '${u}'}}}%%
graph TD;
`:"graph TD;\n";if(l){let t="default",r={[t]:"{0}({1})"};for(let[n,l]of(void 0!==a&&(r[a]="{0}([{1}]):::first"),void 0!==i&&(r[i]="{0}([{1}]):::last"),Object.entries(e))){let e=l.name.split(":").pop()??"",a=o.some(t=>e.startsWith(t)&&e.endsWith(t))?`<p>${e}</p>`:e;Object.keys(l.metadata??{}).length&&(a+=`<hr/><small><em>${Object.entries(l.metadata??{}).map(([e,t])=>`${e} = ${t}`).join("\n")}</em></small>`);let i=(r[n]??r[t]).replace("{0}",s(n)).replace("{1}",a);d+=`	${i}
`}}let h={};for(let e of t){let t=e.source.split(":"),r=e.target.split(":"),a=t.filter((e,t)=>e===r[t]).join(":");h[a]||(h[a]=[]),h[a].push(e)}let p=new Set;function f(e,t){let r=1===e.length&&e[0].source===e[0].target;if(t&&!r){let e=t.split(":").pop();if(p.has(e))throw Error(`Found duplicate subgraph '${e}' -- this likely means that you're reusing a subgraph node with the same name. Please adjust your graph to have subgraph nodes with unique names.`);p.add(e),d+=`	subgraph ${e}
`}for(let t of e){let{source:e,target:r,data:a,conditional:i}=t,n="";if(void 0!==a){let e=a,t=e.split(" ");t.length>c&&(e=Array.from({length:Math.ceil(t.length/c)},(e,r)=>t.slice(r*c,(r+1)*c).join(" ")).join("&nbsp;<br>&nbsp;")),n=i?` -. &nbsp;${e}&nbsp; .-> `:` -- &nbsp;${e}&nbsp; --> `}else n=i?" -.-> ":" --\x3e ";d+=`	${s(e)}${n}${s(r)};
`}for(let e in h)e.startsWith(`${t}:`)&&e!==t&&f(h[e],e);t&&!r&&(d+="	end\n")}for(let e in f(h[""]??[],""),h)e.includes(":")||""===e||f(h[e],e);return l&&(d+=function(e){let t="";for(let[r,a]of Object.entries(e))t+=`	classDef ${r} ${a};
`;return t}(n??{})),d}(n.nodes,n.edges,{firstNode:l?.id,lastNode:u?.id,withStyles:t,curveStyle:r,nodeColors:a,wrapLabelNWords:i})}async drawMermaidPng(e){return l(this.drawMermaid(e),{backgroundColor:e?.backgroundColor})}}function d(e,t=[]){let r=new Set(e.edges.filter(e=>!t.includes(e.source)).map(e=>e.target)),a=[];for(let i of Object.values(e.nodes))t.includes(i.id)||r.has(i.id)||a.push(i);return 1===a.length?a[0]:void 0}function h(e,t=[]){let r=new Set(e.edges.filter(e=>!t.includes(e.target)).map(e=>e.source)),a=[];for(let i of Object.values(e.nodes))t.includes(i.id)||r.has(i.id)||a.push(i);return 1===a.length?a[0]:void 0}},84450:(e,t,r)=>{"use strict";let a=r(73438),i=r(27290),n=r(42699),s=r(44156),o=r(40720),l=r(60301);e.exports=(e,t,r,u)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e===r;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof r&&(r=r.version),e!==r;case"":case"=":case"==":return a(e,r,u);case"!=":return i(e,r,u);case">":return n(e,r,u);case">=":return s(e,r,u);case"<":return o(e,r,u);case"<=":return l(e,r,u);default:throw TypeError(`Invalid operator: ${t}`)}}},84902:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Serializable:()=>s,get_lc_unique_name:()=>n});var a=r(97386);function i(e){return Array.isArray(e)?[...e]:{...e}}function n(e){let t=Object.getPrototypeOf(e);return"function"==typeof e.lc_name&&("function"!=typeof t.lc_name||e.lc_name()!==t.lc_name())?e.lc_name():e.name}class s{static lc_name(){return this.name}get lc_id(){return[...this.lc_namespace,n(this.constructor)]}get lc_secrets(){}get lc_attributes(){}get lc_aliases(){}get lc_serializable_keys(){}constructor(e,...t){Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),void 0!==this.lc_serializable_keys?this.lc_kwargs=Object.fromEntries(Object.entries(e||{}).filter(([e])=>this.lc_serializable_keys?.includes(e))):this.lc_kwargs=e??{}}toJSON(){if(!this.lc_serializable||this.lc_kwargs instanceof s||"object"!=typeof this.lc_kwargs||Array.isArray(this.lc_kwargs))return this.toJSONNotImplemented();let e={},t={},r=Object.keys(this.lc_kwargs).reduce((e,t)=>(e[t]=t in this?this[t]:this.lc_kwargs[t],e),{});for(let a=Object.getPrototypeOf(this);a;a=Object.getPrototypeOf(a))Object.assign(e,Reflect.get(a,"lc_aliases",this)),Object.assign(t,Reflect.get(a,"lc_secrets",this)),Object.assign(r,Reflect.get(a,"lc_attributes",this));return Object.keys(t).forEach(e=>{let t=this,a=r,[i,...n]=e.split(".").reverse();for(let e of n.reverse()){if(!(e in t)||void 0===t[e])return;e in a&&void 0!==a[e]||("object"==typeof t[e]&&null!=t[e]?a[e]={}:Array.isArray(t[e])&&(a[e]=[])),t=t[e],a=a[e]}i in t&&void 0!==t[i]&&(a[i]=a[i]||t[i])}),{lc:1,type:"constructor",id:this.lc_id,kwargs:(0,a.d4)(Object.keys(t).length?function(e,t){let r=i(e);for(let[e,a]of Object.entries(t)){let[t,...n]=e.split(".").reverse(),s=r;for(let e of n.reverse()){if(void 0===s[e])break;s[e]=i(s[e]),s=s[e]}void 0!==s[t]&&(s[t]={lc:1,type:"secret",id:[a]})}return r}(r,t):r,a.$o,e)}}toJSONNotImplemented(){return{lc:1,type:"not_implemented",id:this.lc_id}}}},86605:(e,t,r)=>{"use strict";let a=r(33877);e.exports=(e,t,r)=>a(t,e,r)},87956:(e,t,r)=>{"use strict";function a(e){return!!e&&e.lc_runnable}r.d(t,{G:()=>i,T:()=>a});class i{constructor(e){Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.includeNames=e.includeNames,this.includeTypes=e.includeTypes,this.includeTags=e.includeTags,this.excludeNames=e.excludeNames,this.excludeTypes=e.excludeTypes,this.excludeTags=e.excludeTags}includeEvent(e,t){let r=void 0===this.includeNames&&void 0===this.includeTypes&&void 0===this.includeTags,a=e.tags??[];return void 0!==this.includeNames&&(r=r||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(r=r||this.includeTypes.includes(t)),void 0!==this.includeTags&&(r=r||a.some(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(r=r&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(r=r&&!this.excludeTypes.includes(t)),void 0!==this.excludeTags&&(r=r&&a.every(e=>!this.excludeTags?.includes(e))),r}}},88595:(e,t,r)=>{"use strict";r.r(t),r.d(t,{encodingForModel:()=>d,getEncoding:()=>c});var a=r(18929),i=Object.defineProperty,n=(e,t,r)=>t in e?i(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,s=class{specialTokens;inverseSpecialTokens;patStr;textEncoder=new TextEncoder;textDecoder=new TextDecoder("utf-8");rankMap=new Map;textMap=new Map;constructor(e,t){for(let[t,r]of(this.patStr=e.pat_str,Object.entries(e.bpe_ranks.split("\n").filter(Boolean).reduce((e,t)=>{let[r,a,...i]=t.split(" "),n=Number.parseInt(a,10);return i.forEach((t,r)=>e[t]=n+r),e},{})))){let e=a.toByteArray(t);this.rankMap.set(e.join(","),r),this.textMap.set(r,e)}this.specialTokens={...e.special_tokens,...t},this.inverseSpecialTokens=Object.entries(this.specialTokens).reduce((e,[t,r])=>(e[r]=this.textEncoder.encode(t),e),{})}encode(e,t=[],r="all"){let a=RegExp(this.patStr,"ug"),i=s.specialTokenRegex(Object.keys(this.specialTokens)),n=[],o=new Set("all"===t?Object.keys(this.specialTokens):t),l=new Set("all"===r?Object.keys(this.specialTokens).filter(e=>!o.has(e)):r);if(l.size>0){let t=s.specialTokenRegex([...l]),r=e.match(t);if(null!=r)throw Error(`The text contains a special token that is not allowed: ${r[0]}`)}let u=0;for(;;){let t=null,r=u;for(;i.lastIndex=r,!(null==(t=i.exec(e))||o.has(t[0]));)r=t.index+1;let s=t?.index??e.length;for(let t of e.substring(u,s).matchAll(a)){let e=this.textEncoder.encode(t[0]),r=this.rankMap.get(e.join(","));if(null!=r){n.push(r);continue}n.push(...function(e,t){return 1===e.length?[t.get(e.join(","))]:(function(e,t){let r=Array.from({length:e.length},(e,t)=>({start:t,end:t+1}));for(;r.length>1;){let a=null;for(let i=0;i<r.length-1;i++){let n=e.slice(r[i].start,r[i+1].end),s=t.get(n.join(","));null!=s&&(null==a||s<a[0])&&(a=[s,i])}if(null!=a){let e=a[1];r[e]={start:r[e].start,end:r[e+1].end},r.splice(e+1,1)}else break}return r})(e,t).map(r=>t.get(e.slice(r.start,r.end).join(","))).filter(e=>null!=e)}(e,this.rankMap))}if(null==t)break;let l=this.specialTokens[t[0]];n.push(l),u=t.index+t[0].length}return n}decode(e){let t=[],r=0;for(let a=0;a<e.length;++a){let i=e[a],n=this.textMap.get(i)??this.inverseSpecialTokens[i];null!=n&&(t.push(n),r+=n.length)}let a=new Uint8Array(r),i=0;for(let e of t)a.set(e,i),i+=e.length;return this.textDecoder.decode(a)}};((e,t,r)=>n(e,"symbol"!=typeof t?t+"":t,r))(s,"specialTokenRegex",e=>RegExp(e.map(e=>e.replace(/[\\^$*+?.()|[\]{}]/g,"\\$&")).join("|"),"g"));var o=r(15641);let l={},u=new o.AsyncCaller({});async function c(e){return e in l||(l[e]=u.fetch(`https://tiktoken.pages.dev/js/${e}.json`).then(e=>e.json()).then(e=>new s(e)).catch(t=>{throw delete l[e],t})),await l[e]}async function d(e){return c(function(e){switch(e){case"gpt2":return"gpt2";case"code-cushman-001":case"code-cushman-002":case"code-davinci-001":case"code-davinci-002":case"cushman-codex":case"davinci-codex":case"davinci-002":case"text-davinci-002":case"text-davinci-003":return"p50k_base";case"code-davinci-edit-001":case"text-davinci-edit-001":return"p50k_edit";case"ada":case"babbage":case"babbage-002":case"code-search-ada-code-001":case"code-search-babbage-code-001":case"curie":case"davinci":case"text-ada-001":case"text-babbage-001":case"text-curie-001":case"text-davinci-001":case"text-search-ada-doc-001":case"text-search-babbage-doc-001":case"text-search-curie-doc-001":case"text-search-davinci-doc-001":case"text-similarity-ada-001":case"text-similarity-babbage-001":case"text-similarity-curie-001":case"text-similarity-davinci-001":return"r50k_base";case"gpt-3.5-turbo-instruct-0914":case"gpt-3.5-turbo-instruct":case"gpt-3.5-turbo-16k-0613":case"gpt-3.5-turbo-16k":case"gpt-3.5-turbo-0613":case"gpt-3.5-turbo-0301":case"gpt-3.5-turbo":case"gpt-4-32k-0613":case"gpt-4-32k-0314":case"gpt-4-32k":case"gpt-4-0613":case"gpt-4-0314":case"gpt-4":case"gpt-3.5-turbo-1106":case"gpt-35-turbo":case"gpt-4-1106-preview":case"gpt-4-vision-preview":case"gpt-3.5-turbo-0125":case"gpt-4-turbo":case"gpt-4-turbo-2024-04-09":case"gpt-4-turbo-preview":case"gpt-4-0125-preview":case"text-embedding-ada-002":case"text-embedding-3-small":case"text-embedding-3-large":return"cl100k_base";case"gpt-4o":case"gpt-4o-2024-05-13":case"gpt-4o-2024-08-06":case"gpt-4o-2024-11-20":case"gpt-4o-mini-2024-07-18":case"gpt-4o-mini":case"gpt-4o-search-preview":case"gpt-4o-search-preview-2025-03-11":case"gpt-4o-mini-search-preview":case"gpt-4o-mini-search-preview-2025-03-11":case"gpt-4o-audio-preview":case"gpt-4o-audio-preview-2024-12-17":case"gpt-4o-audio-preview-2024-10-01":case"gpt-4o-mini-audio-preview":case"gpt-4o-mini-audio-preview-2024-12-17":case"o1":case"o1-2024-12-17":case"o1-mini":case"o1-mini-2024-09-12":case"o1-preview":case"o1-preview-2024-09-12":case"o1-pro":case"o1-pro-2025-03-19":case"o3":case"o3-2025-04-16":case"o3-mini":case"o3-mini-2025-01-31":case"o4-mini":case"o4-mini-2025-04-16":case"chatgpt-4o-latest":case"gpt-4o-realtime":case"gpt-4o-realtime-preview-2024-10-01":case"gpt-4o-realtime-preview-2024-12-17":case"gpt-4o-mini-realtime-preview":case"gpt-4o-mini-realtime-preview-2024-12-17":case"gpt-4.1":case"gpt-4.1-2025-04-14":case"gpt-4.1-mini":case"gpt-4.1-mini-2025-04-14":case"gpt-4.1-nano":case"gpt-4.1-nano-2025-04-14":case"gpt-4.5-preview":case"gpt-4.5-preview-2025-02-27":return"o200k_base";default:throw Error("Unknown model")}}(e))}},89136:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(73726);let i=function(e){let t;if(!(0,a.A)(e))throw TypeError("Invalid UUID");let r=new Uint8Array(16);return r[0]=(t=parseInt(e.slice(0,8),16))>>>24,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t,r[4]=(t=parseInt(e.slice(9,13),16))>>>8,r[5]=255&t,r[6]=(t=parseInt(e.slice(14,18),16))>>>8,r[7]=255&t,r[8]=(t=parseInt(e.slice(19,23),16))>>>8,r[9]=255&t,r[10]=(t=parseInt(e.slice(24,36),16))/0x10000000000&255,r[11]=t/0x100000000&255,r[12]=t>>>24&255,r[13]=t>>>16&255,r[14]=t>>>8&255,r[15]=255&t,r}},89386:(e,t,r)=>{"use strict";r.d(t,{k:()=>i});let a=[];for(let e=0;e<256;++e)a.push((e+256).toString(16).slice(1));function i(e,t=0){return(a[e[t+0]]+a[e[t+1]]+a[e[t+2]]+a[e[t+3]]+"-"+a[e[t+4]]+a[e[t+5]]+"-"+a[e[t+6]]+a[e[t+7]]+"-"+a[e[t+8]]+a[e[t+9]]+"-"+a[e[t+10]]+a[e[t+11]]+a[e[t+12]]+a[e[t+13]]+a[e[t+14]]+a[e[t+15]]).toLowerCase()}},89706:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BaseCallbackManager:()=>y,BaseRunManager:()=>b,CallbackManager:()=>O,CallbackManagerForChainRun:()=>w,CallbackManagerForLLMRun:()=>v,CallbackManagerForRetrieverRun:()=>_,CallbackManagerForToolRun:()=>E,TraceGroup:()=>I,ensureHandler:()=>$,parseCallbackConfigArg:()=>g,traceAsGroup:()=>S});var a=r(82116),i=r(60662),n=r(52832),s=r(81412),o=r(56301),l=r(76242),u=r(30413);let c=e=>void 0!==e?e:!!["LANGSMITH_TRACING_V2","LANGCHAIN_TRACING_V2","LANGSMITH_TRACING","LANGCHAIN_TRACING"].find(e=>"true"===(0,o.getEnvironmentVariable)(e));var d=r(65164);r(65109);var h=r(4346);function p(e){let t=(0,h.X0)();if(void 0===t)return;let r=t.getStore();return r?.[h.hr]?.[e]}let f=Symbol("lc:configure_hooks"),m=()=>p(f)||[];function g(e){return e?Array.isArray(e)||"name"in e?{callbacks:e}:e:{}}class y{setHandler(e){return this.setHandlers([e])}}class b{constructor(e,t,r,a,i,n,s,o){Object.defineProperty(this,"runId",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"handlers",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"inheritableHandlers",{enumerable:!0,configurable:!0,writable:!0,value:r}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:a}),Object.defineProperty(this,"inheritableTags",{enumerable:!0,configurable:!0,writable:!0,value:i}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:n}),Object.defineProperty(this,"inheritableMetadata",{enumerable:!0,configurable:!0,writable:!0,value:s}),Object.defineProperty(this,"_parentRunId",{enumerable:!0,configurable:!0,writable:!0,value:o})}get parentRunId(){return this._parentRunId}async handleText(e){await Promise.all(this.handlers.map(t=>(0,u.consumeCallback)(async()=>{try{await t.handleText?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleText: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleCustomEvent(e,t,r,a,i){await Promise.all(this.handlers.map(r=>(0,u.consumeCallback)(async()=>{try{await r.handleCustomEvent?.(e,t,this.runId,this.tags,this.metadata)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleCustomEvent: ${e}`),r.raiseError)throw e}},r.awaitHandlers)))}}class _ extends b{getChild(e){let t=new O(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleRetrieverEnd(e){await Promise.all(this.handlers.map(t=>(0,u.consumeCallback)(async()=>{if(!t.ignoreRetriever)try{await t.handleRetrieverEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleRetriever`),t.raiseError)throw e}},t.awaitHandlers)))}async handleRetrieverError(e){await Promise.all(this.handlers.map(t=>(0,u.consumeCallback)(async()=>{if(!t.ignoreRetriever)try{await t.handleRetrieverError?.(e,this.runId,this._parentRunId,this.tags)}catch(r){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleRetrieverError: ${r}`),t.raiseError)throw e}},t.awaitHandlers)))}}class v extends b{async handleLLMNewToken(e,t,r,a,i,n){await Promise.all(this.handlers.map(r=>(0,u.consumeCallback)(async()=>{if(!r.ignoreLLM)try{await r.handleLLMNewToken?.(e,t??{prompt:0,completion:0},this.runId,this._parentRunId,this.tags,n)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleLLMNewToken: ${e}`),r.raiseError)throw e}},r.awaitHandlers)))}async handleLLMError(e,t,r,a,i){await Promise.all(this.handlers.map(t=>(0,u.consumeCallback)(async()=>{if(!t.ignoreLLM)try{await t.handleLLMError?.(e,this.runId,this._parentRunId,this.tags,i)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleLLMError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleLLMEnd(e,t,r,a,i){await Promise.all(this.handlers.map(t=>(0,u.consumeCallback)(async()=>{if(!t.ignoreLLM)try{await t.handleLLMEnd?.(e,this.runId,this._parentRunId,this.tags,i)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleLLMEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class w extends b{getChild(e){let t=new O(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleChainError(e,t,r,a,i){await Promise.all(this.handlers.map(t=>(0,u.consumeCallback)(async()=>{if(!t.ignoreChain)try{await t.handleChainError?.(e,this.runId,this._parentRunId,this.tags,i)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleChainError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleChainEnd(e,t,r,a,i){await Promise.all(this.handlers.map(t=>(0,u.consumeCallback)(async()=>{if(!t.ignoreChain)try{await t.handleChainEnd?.(e,this.runId,this._parentRunId,this.tags,i)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleChainEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleAgentAction(e){await Promise.all(this.handlers.map(t=>(0,u.consumeCallback)(async()=>{if(!t.ignoreAgent)try{await t.handleAgentAction?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleAgentAction: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleAgentEnd(e){await Promise.all(this.handlers.map(t=>(0,u.consumeCallback)(async()=>{if(!t.ignoreAgent)try{await t.handleAgentEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleAgentEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class E extends b{getChild(e){let t=new O(this.runId);return t.setHandlers(this.inheritableHandlers),t.addTags(this.inheritableTags),t.addMetadata(this.inheritableMetadata),e&&t.addTags([e],!1),t}async handleToolError(e){await Promise.all(this.handlers.map(t=>(0,u.consumeCallback)(async()=>{if(!t.ignoreAgent)try{await t.handleToolError?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleToolError: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}async handleToolEnd(e){await Promise.all(this.handlers.map(t=>(0,u.consumeCallback)(async()=>{if(!t.ignoreAgent)try{await t.handleToolEnd?.(e,this.runId,this._parentRunId,this.tags)}catch(e){if((t.raiseError?console.error:console.warn)(`Error in handler ${t.constructor.name}, handleToolEnd: ${e}`),t.raiseError)throw e}},t.awaitHandlers)))}}class O extends y{constructor(e,t){super(),Object.defineProperty(this,"handlers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"inheritableHandlers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"inheritableTags",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"inheritableMetadata",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"callback_manager"}),Object.defineProperty(this,"_parentRunId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.handlers=t?.handlers??this.handlers,this.inheritableHandlers=t?.inheritableHandlers??this.inheritableHandlers,this.tags=t?.tags??this.tags,this.inheritableTags=t?.inheritableTags??this.inheritableTags,this.metadata=t?.metadata??this.metadata,this.inheritableMetadata=t?.inheritableMetadata??this.inheritableMetadata,this._parentRunId=e}getParentRunId(){return this._parentRunId}async handleLLMStart(e,t,r,i,n,s,o,l){return Promise.all(t.map(async(t,i)=>{let s=0===i&&r?r:(0,a.A)();return await Promise.all(this.handlers.map(r=>{if(!r.ignoreLLM)return(0,d.isBaseTracer)(r)&&r._createRunForLLMStart(e,[t],s,this._parentRunId,n,this.tags,this.metadata,l),(0,u.consumeCallback)(async()=>{try{await r.handleLLMStart?.(e,[t],s,this._parentRunId,n,this.tags,this.metadata,l)}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleLLMStart: ${e}`),r.raiseError)throw e}},r.awaitHandlers)})),new v(s,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}))}async handleChatModelStart(e,t,r,i,n,o,l,c){return Promise.all(t.map(async(t,i)=>{let o=0===i&&r?r:(0,a.A)();return await Promise.all(this.handlers.map(r=>{if(!r.ignoreLLM)return(0,d.isBaseTracer)(r)&&r._createRunForChatModelStart(e,[t],o,this._parentRunId,n,this.tags,this.metadata,c),(0,u.consumeCallback)(async()=>{try{if(r.handleChatModelStart)await r.handleChatModelStart?.(e,[t],o,this._parentRunId,n,this.tags,this.metadata,c);else if(r.handleLLMStart){let a=(0,s.Sw)(t);await r.handleLLMStart?.(e,[a],o,this._parentRunId,n,this.tags,this.metadata,c)}}catch(e){if((r.raiseError?console.error:console.warn)(`Error in handler ${r.constructor.name}, handleLLMStart: ${e}`),r.raiseError)throw e}},r.awaitHandlers)})),new v(o,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}))}async handleChainStart(e,t,r=(0,a.A)(),i,n,s,o){return await Promise.all(this.handlers.map(a=>{if(!a.ignoreChain)return(0,d.isBaseTracer)(a)&&a._createRunForChainStart(e,t,r,this._parentRunId,this.tags,this.metadata,i,o),(0,u.consumeCallback)(async()=>{try{await a.handleChainStart?.(e,t,r,this._parentRunId,this.tags,this.metadata,i,o)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleChainStart: ${e}`),a.raiseError)throw e}},a.awaitHandlers)})),new w(r,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleToolStart(e,t,r=(0,a.A)(),i,n,s,o){return await Promise.all(this.handlers.map(a=>{if(!a.ignoreAgent)return(0,d.isBaseTracer)(a)&&a._createRunForToolStart(e,t,r,this._parentRunId,this.tags,this.metadata,o),(0,u.consumeCallback)(async()=>{try{await a.handleToolStart?.(e,t,r,this._parentRunId,this.tags,this.metadata,o)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleToolStart: ${e}`),a.raiseError)throw e}},a.awaitHandlers)})),new E(r,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleRetrieverStart(e,t,r=(0,a.A)(),i,n,s,o){return await Promise.all(this.handlers.map(a=>{if(!a.ignoreRetriever)return(0,d.isBaseTracer)(a)&&a._createRunForRetrieverStart(e,t,r,this._parentRunId,this.tags,this.metadata,o),(0,u.consumeCallback)(async()=>{try{await a.handleRetrieverStart?.(e,t,r,this._parentRunId,this.tags,this.metadata,o)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleRetrieverStart: ${e}`),a.raiseError)throw e}},a.awaitHandlers)})),new _(r,this.handlers,this.inheritableHandlers,this.tags,this.inheritableTags,this.metadata,this.inheritableMetadata,this._parentRunId)}async handleCustomEvent(e,t,r,a,i){await Promise.all(this.handlers.map(a=>(0,u.consumeCallback)(async()=>{if(!a.ignoreCustomEvent)try{await a.handleCustomEvent?.(e,t,r,this.tags,this.metadata)}catch(e){if((a.raiseError?console.error:console.warn)(`Error in handler ${a.constructor.name}, handleCustomEvent: ${e}`),a.raiseError)throw e}},a.awaitHandlers)))}addHandler(e,t=!0){this.handlers.push(e),t&&this.inheritableHandlers.push(e)}removeHandler(e){this.handlers=this.handlers.filter(t=>t!==e),this.inheritableHandlers=this.inheritableHandlers.filter(t=>t!==e)}setHandlers(e,t=!0){for(let r of(this.handlers=[],this.inheritableHandlers=[],e))this.addHandler(r,t)}addTags(e,t=!0){this.removeTags(e),this.tags.push(...e),t&&this.inheritableTags.push(...e)}removeTags(e){this.tags=this.tags.filter(t=>!e.includes(t)),this.inheritableTags=this.inheritableTags.filter(t=>!e.includes(t))}addMetadata(e,t=!0){this.metadata={...this.metadata,...e},t&&(this.inheritableMetadata={...this.inheritableMetadata,...e})}removeMetadata(e){for(let t of Object.keys(e))delete this.metadata[t],delete this.inheritableMetadata[t]}copy(e=[],t=!0){let r=new O(this._parentRunId);for(let e of this.handlers){let t=this.inheritableHandlers.includes(e);r.addHandler(e,t)}for(let e of this.tags){let t=this.inheritableTags.includes(e);r.addTags([e],t)}for(let e of Object.keys(this.metadata)){let t=Object.keys(this.inheritableMetadata).includes(e);r.addMetadata({[e]:this.metadata[e]},t)}for(let a of e)r.handlers.filter(e=>"console_callback_handler"===e.name).some(e=>e.name===a.name)||r.addHandler(a,t);return r}static fromHandlers(e){class t extends i.BaseCallbackHandler{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:(0,a.A)()}),Object.assign(this,e)}}let r=new this;return r.addHandler(new t),r}static configure(e,t,r,a,i,n,s){return this._configureSync(e,t,r,a,i,n,s)}static _configureSync(e,t,r,a,s,u,d){let h;(e||t)&&(Array.isArray(e)||!e?(h=new O).setHandlers(e?.map($)??[],!0):h=e,h=h.copy(Array.isArray(t)?t.map($):t?.handlers,!1));let f="true"===(0,o.getEnvironmentVariable)("LANGCHAIN_VERBOSE")||d?.verbose,g=l.LangChainTracer.getTraceableRunTree()?.tracingEnabled||c(),y=g||((0,o.getEnvironmentVariable)("LANGCHAIN_TRACING")??!1);if(f||y){if(h||(h=new O),f&&!h.handlers.some(e=>e.name===n.ConsoleCallbackHandler.prototype.name)){let e=new n.ConsoleCallbackHandler;h.addHandler(e,!0)}if(y&&!h.handlers.some(e=>"langchain_tracer"===e.name)&&g){let e=new l.LangChainTracer;h.addHandler(e,!0),h._parentRunId=l.LangChainTracer.getTraceableRunTree()?.id??h._parentRunId}}for(let{contextVar:e,inheritable:t=!0,handlerClass:r,envVar:a}of m()){let n,s=a&&"true"===(0,o.getEnvironmentVariable)(a)&&r,l=void 0!==e?p(e):void 0;l&&(0,i.isBaseCallbackHandler)(l)?n=l:s&&(n=new r({})),void 0!==n&&(h||(h=new O),h.handlers.some(e=>e.name===n.name)||h.addHandler(n,t))}return(r||a)&&h&&(h.addTags(r??[]),h.addTags(a??[],!1)),(s||u)&&h&&(h.addMetadata(s??{}),h.addMetadata(u??{},!1)),h}}function $(e){return"name"in e?e:i.BaseCallbackHandler.fromMethods(e)}class I{constructor(e,t){Object.defineProperty(this,"groupName",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"options",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"runManager",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}async getTraceGroupCallbackManager(e,t,r){let a=new l.LangChainTracer(r),i=await O.configure([a]),n=await i?.handleChainStart({lc:1,type:"not_implemented",id:["langchain","callbacks","groups",e]},t??{});if(!n)throw Error("Failed to create run group callback manager.");return n}async start(e){return this.runManager||(this.runManager=await this.getTraceGroupCallbackManager(this.groupName,e,this.options)),this.runManager.getChild()}async error(e){this.runManager&&(await this.runManager.handleChainError(e),this.runManager=void 0)}async end(e){this.runManager&&(await this.runManager.handleChainEnd(e??{}),this.runManager=void 0)}}async function S(e,t,...r){let a=new I(e.name,e),i=await a.start({...r});try{let e=await t(i,...r);return await a.end(e&&!Array.isArray(e)&&"object"==typeof e?e:{output:e}),e}catch(e){throw await a.error(e),e}}},90163:(e,t,r)=>{"use strict";r.d(t,{YN:()=>j,B2:()=>B,fJ:()=>P,Vi:()=>R,jY:()=>z,ck:()=>L,Pq:()=>U,Fm:()=>H,AO:()=>N,zZ:()=>C,pG:()=>G,lH:()=>D,GH:()=>x,Bp:()=>F});var a=r(45697),i=r(63611),n=r(82116),s=r(37664),o=r(28506),l=r(65164),u=r(72180),c=r(16275),d=r(38022);function h({name:e,serialized:t}){return void 0!==e?e:t?.name!==void 0?t.name:t?.id!==void 0&&Array.isArray(t?.id)?t.id[t.id.length-1]:"Unnamed"}let p=e=>"event_stream_tracer"===e.name;class f extends l.BaseTracer{constructor(e){super({_awaitHandler:!0,...e}),Object.defineProperty(this,"autoClose",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"includeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"includeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeNames",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTypes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"excludeTags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"runInfoMap",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"tappedPromises",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"transformStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"writer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"receiveStream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"event_stream_tracer"}),Object.defineProperty(this,"lc_prefer_streaming",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.autoClose=e?.autoClose??!0,this.includeNames=e?.includeNames,this.includeTypes=e?.includeTypes,this.includeTags=e?.includeTags,this.excludeNames=e?.excludeNames,this.excludeTypes=e?.excludeTypes,this.excludeTags=e?.excludeTags,this.transformStream=new TransformStream,this.writer=this.transformStream.writable.getWriter(),this.receiveStream=u.IterableReadableStream.fromReadableStream(this.transformStream.readable)}[Symbol.asyncIterator](){return this.receiveStream}async persistRun(e){}_includeRun(e){let t=e.tags??[],r=void 0===this.includeNames&&void 0===this.includeTags&&void 0===this.includeTypes;return void 0!==this.includeNames&&(r=r||this.includeNames.includes(e.name)),void 0!==this.includeTypes&&(r=r||this.includeTypes.includes(e.runType)),void 0!==this.includeTags&&(r=r||void 0!==t.find(e=>this.includeTags?.includes(e))),void 0!==this.excludeNames&&(r=r&&!this.excludeNames.includes(e.name)),void 0!==this.excludeTypes&&(r=r&&!this.excludeTypes.includes(e.runType)),void 0!==this.excludeTags&&(r=r&&t.every(e=>!this.excludeTags?.includes(e))),r}async *tapOutputIterable(e,t){let r=await t.next();if(r.done)return;let a=this.runInfoMap.get(e);if(void 0===a)return void(yield r.value);function i(e,t){return"llm"===e&&"string"==typeof t?new d.GenerationChunk({text:t}):t}let n=this.tappedPromises.get(e);if(void 0===n){let s;n=new Promise(e=>{s=e}),this.tappedPromises.set(e,n);try{let n={event:`on_${a.runType}_stream`,run_id:e,name:a.name,tags:a.tags,metadata:a.metadata,data:{}};for await(let e of(await this.send({...n,data:{chunk:i(a.runType,r.value)}},a),yield r.value,t))"tool"!==a.runType&&"retriever"!==a.runType&&await this.send({...n,data:{chunk:i(a.runType,e)}},a),yield e}finally{s()}}else for await(let e of(yield r.value,t))yield e}async send(e,t){this._includeRun(t)&&await this.writer.write(e)}async sendEndEvent(e,t){let r=this.tappedPromises.get(e.run_id);void 0!==r?r.then(()=>{this.send(e,t)}):await this.send(e,t)}async onLLMStart(e){let t=h(e),r=void 0!==e.inputs.messages?"chat_model":"llm",a={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:r,inputs:e.inputs};this.runInfoMap.set(e.id,a);let i=`on_${r}_start`;await this.send({event:i,data:{input:e.inputs},name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},a)}async onLLMNewToken(e,t,r){let a,i,n=this.runInfoMap.get(e.id);if(void 0===n)throw Error(`onLLMNewToken: Run ID ${e.id} not found in run map.`);if(1!==this.runInfoMap.size){if("chat_model"===n.runType)i="on_chat_model_stream",a=r?.chunk===void 0?new c.H({content:t,id:`run-${e.id}`}):r.chunk.message;else if("llm"===n.runType)i="on_llm_stream",a=r?.chunk===void 0?new d.GenerationChunk({text:t}):r.chunk;else throw Error(`Unexpected run type ${n.runType}`);await this.send({event:i,data:{chunk:a},run_id:e.id,name:n.name,tags:n.tags,metadata:n.metadata},n)}}async onLLMEnd(e){let t,r,a=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===a)throw Error(`onLLMEnd: Run ID ${e.id} not found in run map.`);let i=e.outputs?.generations;if("chat_model"===a.runType){for(let e of i??[]){if(void 0!==r)break;r=e[0]?.message}t="on_chat_model_end"}else if("llm"===a.runType)r={generations:i?.map(e=>e.map(e=>({text:e.text,generationInfo:e.generationInfo}))),llmOutput:e.outputs?.llmOutput??{}},t="on_llm_end";else throw Error(`onLLMEnd: Unexpected run type: ${a.runType}`);await this.sendEndEvent({event:t,data:{output:r,input:a.inputs},run_id:e.id,name:a.name,tags:a.tags,metadata:a.metadata},a)}async onChainStart(e){let t=h(e),r=e.run_type??"chain",a={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:e.run_type},i={};""===e.inputs.input&&1===Object.keys(e.inputs).length?(i={},a.inputs={}):void 0!==e.inputs.input?(i.input=e.inputs.input,a.inputs=e.inputs.input):(i.input=e.inputs,a.inputs=e.inputs),this.runInfoMap.set(e.id,a),await this.send({event:`on_${r}_start`,data:i,name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},a)}async onChainEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onChainEnd: Run ID ${e.id} not found in run map.`);let r=`on_${e.run_type}_end`,a=e.inputs??t.inputs??{},i={output:e.outputs?.output??e.outputs,input:a};a.input&&1===Object.keys(a).length&&(i.input=a.input,t.inputs=a.input),await this.sendEndEvent({event:r,data:i,run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata??{}},t)}async onToolStart(e){let t=h(e),r={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:"tool",inputs:e.inputs??{}};this.runInfoMap.set(e.id,r),await this.send({event:"on_tool_start",data:{input:e.inputs??{}},name:t,run_id:e.id,tags:e.tags??[],metadata:e.extra?.metadata??{}},r)}async onToolEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onToolEnd: Run ID ${e.id} not found in run map.`);if(void 0===t.inputs)throw Error(`onToolEnd: Run ID ${e.id} is a tool call, and is expected to have traced inputs.`);let r=e.outputs?.output===void 0?e.outputs:e.outputs.output;await this.sendEndEvent({event:"on_tool_end",data:{output:r,input:t.inputs},run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata},t)}async onRetrieverStart(e){let t=h(e),r={tags:e.tags??[],metadata:e.extra?.metadata??{},name:t,runType:"retriever",inputs:{query:e.inputs.query}};this.runInfoMap.set(e.id,r),await this.send({event:"on_retriever_start",data:{input:{query:e.inputs.query}},name:t,tags:e.tags??[],run_id:e.id,metadata:e.extra?.metadata??{}},r)}async onRetrieverEnd(e){let t=this.runInfoMap.get(e.id);if(this.runInfoMap.delete(e.id),void 0===t)throw Error(`onRetrieverEnd: Run ID ${e.id} not found in run map.`);await this.sendEndEvent({event:"on_retriever_end",data:{output:e.outputs?.documents??e.outputs,input:t.inputs},run_id:e.id,name:t.name,tags:t.tags,metadata:t.metadata},t)}async handleCustomEvent(e,t,r){let a=this.runInfoMap.get(r);if(void 0===a)throw Error(`handleCustomEvent: Run ID ${r} not found in run map.`);await this.send({event:"on_custom_event",run_id:r,name:e,tags:a.tags,metadata:a.metadata,data:t},a)}async finish(){Promise.all([...this.tappedPromises.values()]).finally(()=>{this.writer.close()})}}var m=r(84902),g=r(28604),y=r(27935),b=r(15641);class _ extends l.BaseTracer{constructor({config:e,onStart:t,onEnd:r,onError:a}){super({_awaitHandler:!0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"RootListenersTracer"}),Object.defineProperty(this,"rootId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnStart",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnEnd",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"argOnError",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.config=e,this.argOnStart=t,this.argOnEnd=r,this.argOnError=a}persistRun(e){return Promise.resolve()}async onRunCreate(e){!this.rootId&&(this.rootId=e.id,this.argOnStart&&await this.argOnStart(e,this.config))}async onRunUpdate(e){e.id===this.rootId&&(e.error?this.argOnError&&await this.argOnError(e,this.config):this.argOnEnd&&await this.argOnEnd(e,this.config))}}var v=r(87956),w=r(22123),E=r(82506);function O(e){return"object"==typeof e&&null!==e&&"function"==typeof e[Symbol.iterator]&&"function"==typeof e.next}let $=e=>null!=e&&"object"==typeof e&&"next"in e&&"function"==typeof e.next;function I(e){return"object"==typeof e&&null!==e&&"function"==typeof e[Symbol.asyncIterator]}function*S(e,t){for(;;){let{value:r,done:a}=w.Nx.runWithConfig((0,y.DY)(e),t.next.bind(t),!0);if(a)break;yield r}}async function*k(e,t){let r=t[Symbol.asyncIterator]();for(;;){let{value:a,done:i}=await w.Nx.runWithConfig((0,y.DY)(e),r.next.bind(t),!0);if(i)break;yield a}}var A=r(48457),T=r(60157);function x(e,t){return!e||Array.isArray(e)||e instanceof Date||"object"!=typeof e?{[t]:e}:e}class j extends m.Serializable{constructor(){super(...arguments),Object.defineProperty(this,"lc_runnable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}getName(e){let t=this.name??this.constructor.lc_name()??this.constructor.name;return e?`${t}${e}`:t}bind(e){return new P({bound:this,kwargs:e,config:{}})}map(){return new R({bound:this})}withRetry(e){return new N({bound:this,kwargs:{},config:{},maxAttemptNumber:e?.stopAfterAttempt,...e})}withConfig(e){return new P({bound:this,config:e,kwargs:{}})}withFallbacks(e){return new D({runnable:this,fallbacks:Array.isArray(e)?e:e.fallbacks})}_getOptionsList(e,t=0){if(Array.isArray(e)&&e.length!==t)throw Error(`Passed "options" must be an array with the same length as the inputs, but got ${e.length} options for ${t} inputs`);if(Array.isArray(e))return e.map(y.ZI);if(t>1&&!Array.isArray(e)&&e.runId){console.warn("Provided runId will be used only for the first element of the batch.");let r=Object.fromEntries(Object.entries(e).filter(([e])=>"runId"!==e));return Array.from({length:t},(t,a)=>(0,y.ZI)(0===a?e:r))}return Array.from({length:t},()=>(0,y.ZI)(e))}async batch(e,t,r){let a=this._getOptionsList(t??{},e.length),i=a[0]?.maxConcurrency??r?.maxConcurrency,n=new b.AsyncCaller({maxConcurrency:i,onFailedAttempt:e=>{throw e}});return Promise.all(e.map((e,t)=>n.call(async()=>{try{return await this.invoke(e,a[t])}catch(e){if(r?.returnExceptions)return e;throw e}})))}async *_streamIterator(e,t){yield this.invoke(e,t)}async stream(e,t){let r=(0,y.ZI)(t),a=new u.AsyncGeneratorWithSetup({generator:this._streamIterator(e,r),config:r});return await a.setup,u.IterableReadableStream.fromAsyncGenerator(a)}_separateRunnableConfigFromCallOptions(e){let t;t=void 0===e?(0,y.ZI)(e):(0,y.ZI)({callbacks:e.callbacks,tags:e.tags,metadata:e.metadata,runName:e.runName,configurable:e.configurable,recursionLimit:e.recursionLimit,maxConcurrency:e.maxConcurrency,runId:e.runId,timeout:e.timeout,signal:e.signal});let r={...e};return delete r.callbacks,delete r.tags,delete r.metadata,delete r.runName,delete r.configurable,delete r.recursionLimit,delete r.maxConcurrency,delete r.runId,delete r.timeout,delete r.signal,[t,r]}async _callWithConfig(e,t,r){let a,i=(0,y.ZI)(r),n=await (0,y.kJ)(i),s=await n?.handleChainStart(this.toJSON(),x(t,"input"),i.runId,i?.runType,void 0,void 0,i?.runName??this.getName());delete i.runId;try{let n=e.call(this,t,i,s);a=await (0,g.o)(n,r?.signal)}catch(e){throw await s?.handleChainError(e),e}return await s?.handleChainEnd(x(a,"output")),a}async _batchWithConfig(e,t,r,a){let i,n=this._getOptionsList(r??{},t.length),s=await Promise.all(n.map(y.kJ)),o=await Promise.all(s.map(async(e,r)=>{let a=await e?.handleChainStart(this.toJSON(),x(t[r],"input"),n[r].runId,n[r].runType,void 0,void 0,n[r].runName??this.getName());return delete n[r].runId,a}));try{let r=e.call(this,t,n,o,a);i=await (0,g.o)(r,n?.[0]?.signal)}catch(e){throw await Promise.all(o.map(t=>t?.handleChainError(e))),e}return await Promise.all(o.map(e=>e?.handleChainEnd(x(i,"output")))),i}async *_transformStreamWithConfig(e,t,r){let a,i,n,s=!0,l=!0,c=(0,y.ZI)(r),d=await (0,y.kJ)(c);async function*h(){for await(let t of e){if(s)if(void 0===a)a=t;else try{a=(0,u.concat)(a,t)}catch{a=void 0,s=!1}yield t}}try{let e=await (0,u.pipeGeneratorWithSetup)(t.bind(this),h(),async()=>d?.handleChainStart(this.toJSON(),{input:""},c.runId,c.runType,void 0,void 0,c.runName??this.getName()),r?.signal,c);delete c.runId,n=e.setup;let a=n?.handlers.find(p),s=e.output;void 0!==a&&void 0!==n&&(s=a.tapOutputIterable(n.runId,s));let f=n?.handlers.find(o.isLogStreamHandler);for await(let e of(void 0!==f&&void 0!==n&&(s=f.tapOutputIterable(n.runId,s)),s))if(yield e,l)if(void 0===i)i=e;else try{i=(0,u.concat)(i,e)}catch{i=void 0,l=!1}}catch(e){throw await n?.handleChainError(e,void 0,void 0,void 0,{inputs:x(a,"input")}),e}await n?.handleChainEnd(i??{},void 0,void 0,void 0,{inputs:x(a,"input")})}getGraph(e){let t=new E.T,r=t.addNode({name:`${this.getName()}Input`,schema:a.z.any()}),i=t.addNode(this),n=t.addNode({name:`${this.getName()}Output`,schema:a.z.any()});return t.addEdge(r,i),t.addEdge(i,n),t}pipe(e){return new C({first:this,last:F(e)})}pick(e){return this.pipe(new H(e))}assign(e){return this.pipe(new B(new L({steps:e})))}async *transform(e,t){let r;for await(let t of e)r=void 0===r?t:(0,u.concat)(r,t);yield*this._streamIterator(r,(0,y.ZI)(t))}async *streamLog(e,t,r){let a=new o.LogStreamCallbackHandler({...r,autoClose:!1,_schemaFormat:"original"}),i=(0,y.ZI)(t);yield*this._streamLog(e,a,i)}async *_streamLog(e,t,r){let{callbacks:a}=r;if(void 0===a)r.callbacks=[t];else if(Array.isArray(a))r.callbacks=a.concat([t]);else{let e=a.copy();e.addHandler(t,!0),r.callbacks=e}let i=this.stream(e,r),n=async function(){try{for await(let e of(await i)){let r=new o.RunLogPatch({ops:[{op:"add",path:"/streamed_output/-",value:e}]});await t.writer.write(r)}}finally{await t.writer.close()}}();try{for await(let e of t)yield e}finally{await n}}streamEvents(e,t,r){let a;if("v1"===t.version)a=this._streamEventsV1(e,t,r);else if("v2"===t.version)a=this._streamEventsV2(e,t,r);else throw Error('Only versions "v1" and "v2" of the schema are currently supported.');if("text/event-stream"!==t.encoding)return u.IterableReadableStream.fromAsyncGenerator(a);var i=a;let n=new TextEncoder,s=new ReadableStream({async start(e){for await(let t of i)e.enqueue(n.encode(`event: data
data: ${JSON.stringify(t)}

`));e.enqueue(n.encode("event: end\n\n")),e.close()}});return u.IterableReadableStream.fromReadableStream(s)}async *_streamEventsV2(e,t,r){let a,i=new f({...r,autoClose:!1}),s=(0,y.ZI)(t),o=s.runId??(0,n.A)();s.runId=o;let l=s.callbacks;if(void 0===l)s.callbacks=[i];else if(Array.isArray(l))s.callbacks=l.concat(i);else{let e=l.copy();e.addHandler(i,!0),s.callbacks=e}let u=new AbortController,c=this,d=async function(){try{let r;t?.signal?"any"in AbortSignal?r=AbortSignal.any([u.signal,t.signal]):(r=t.signal,t.signal.addEventListener("abort",()=>{u.abort()},{once:!0})):r=u.signal;let a=await c.stream(e,{...s,signal:r});for await(let e of i.tapOutputIterable(o,a))if(u.signal.aborted)break}finally{await i.finish()}}(),h=!1;try{for await(let t of i){if(!h){t.data.input=e,h=!0,a=t.run_id,yield t;continue}t.run_id===a&&t.event.endsWith("_end")&&t.data?.input&&delete t.data.input,yield t}}finally{u.abort(),await d}}async *_streamEventsV1(e,t,r){let a,i=!1,n=(0,y.ZI)(t),s=n.tags??[],l=n.metadata??{},u=n.runName??this.getName(),c=new o.LogStreamCallbackHandler({...r,autoClose:!1,_schemaFormat:"streaming_events"}),d=new v.G({...r});for await(let t of this._streamLog(e,c,n)){if(void 0===(a=a?a.concat(t):o.RunLog.fromRunLogPatch(t)).state)throw Error('Internal error: "streamEvents" state is missing. Please open a bug report.');if(!i){i=!0;let t={...a.state},r={run_id:t.id,event:`on_${t.type}_start`,name:u,tags:s,metadata:l,data:{input:e}};d.includeEvent(r,t.type)&&(yield r)}for(let e of[...new Set(t.ops.filter(e=>e.path.startsWith("/logs/")).map(e=>e.path.split("/")[2]))]){let t,r={},i=a.state.logs[e];if("start"==(t=void 0===i.end_time?i.streamed_output.length>0?"stream":"start":"end"))void 0!==i.inputs&&(r.input=i.inputs);else if("end"===t)void 0!==i.inputs&&(r.input=i.inputs),r.output=i.final_output;else if("stream"===t){let e=i.streamed_output.length;if(1!==e)throw Error(`Expected exactly one chunk of streamed output, got ${e} instead. Encountered in: "${i.name}"`);r={chunk:i.streamed_output[0]},i.streamed_output=[]}yield{event:`on_${i.type}_${t}`,name:i.name,run_id:i.id,tags:i.tags,metadata:i.metadata,data:r}}let{state:r}=a;if(r.streamed_output.length>0){let e=r.streamed_output.length;if(1!==e)throw Error(`Expected exactly one chunk of streamed output, got ${e} instead. Encountered in: "${r.name}"`);let t={chunk:r.streamed_output[0]};r.streamed_output=[];let a={event:`on_${r.type}_stream`,run_id:r.id,tags:s,metadata:l,name:u,data:t};d.includeEvent(a,r.type)&&(yield a)}}let h=a?.state;if(void 0!==h){let e={event:`on_${h.type}_end`,name:u,run_id:h.id,tags:s,metadata:l,data:{output:h.final_output}};d.includeEvent(e,h.type)&&(yield e)}}static isRunnable(e){return(0,v.T)(e)}withListeners({onStart:e,onEnd:t,onError:r}){return new P({bound:this,config:{},configFactories:[a=>({callbacks:[new _({config:a,onStart:e,onEnd:t,onError:r})]})]})}asTool(e){var t=this,r=e;let i=r.name??t.getName(),n=r.description??(0,T.cg)(r.schema);return new G((0,T.yQ)(r.schema)?{name:i,description:n,schema:a.z.object({input:a.z.string()}).transform(e=>e.input),bound:t}:{name:i,description:n,schema:r.schema,bound:t})}}class P extends j{static lc_name(){return"RunnableBinding"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"bound",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"kwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"configFactories",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.bound=e.bound,this.kwargs=e.kwargs,this.config=e.config,this.configFactories=e.configFactories}getName(e){return this.bound.getName(e)}async _mergeConfig(...e){let t=(0,y.SV)(this.config,...e);return(0,y.SV)(t,...this.configFactories?await Promise.all(this.configFactories.map(async e=>await e(t))):[])}bind(e){return new this.constructor({bound:this.bound,kwargs:{...this.kwargs,...e},config:this.config})}withConfig(e){return new this.constructor({bound:this.bound,kwargs:this.kwargs,config:{...this.config,...e}})}withRetry(e){return new N({bound:this.bound,kwargs:this.kwargs,config:this.config,maxAttemptNumber:e?.stopAfterAttempt,...e})}async invoke(e,t){return this.bound.invoke(e,await this._mergeConfig((0,y.ZI)(t),this.kwargs))}async batch(e,t,r){let a=Array.isArray(t)?await Promise.all(t.map(async e=>this._mergeConfig((0,y.ZI)(e),this.kwargs))):await this._mergeConfig((0,y.ZI)(t),this.kwargs);return this.bound.batch(e,a,r)}async *_streamIterator(e,t){yield*this.bound._streamIterator(e,await this._mergeConfig((0,y.ZI)(t),this.kwargs))}async stream(e,t){return this.bound.stream(e,await this._mergeConfig((0,y.ZI)(t),this.kwargs))}async *transform(e,t){yield*this.bound.transform(e,await this._mergeConfig((0,y.ZI)(t),this.kwargs))}streamEvents(e,t,r){let a=this,i=async function*(){yield*a.bound.streamEvents(e,{...await a._mergeConfig((0,y.ZI)(t),a.kwargs),version:t.version},r)};return u.IterableReadableStream.fromAsyncGenerator(i())}static isRunnableBinding(e){return e.bound&&j.isRunnable(e.bound)}withListeners({onStart:e,onEnd:t,onError:r}){return new P({bound:this.bound,kwargs:this.kwargs,config:this.config,configFactories:[a=>({callbacks:[new _({config:a,onStart:e,onEnd:t,onError:r})]})]})}}class R extends j{static lc_name(){return"RunnableEach"}constructor(e){super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"bound",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.bound=e.bound}bind(e){return new R({bound:this.bound.bind(e)})}async invoke(e,t){return this._callWithConfig(this._invoke.bind(this),e,t)}async _invoke(e,t,r){return this.bound.batch(e,(0,y.tn)(t,{callbacks:r?.getChild()}))}withListeners({onStart:e,onEnd:t,onError:r}){return new R({bound:this.bound.withListeners({onStart:e,onEnd:t,onError:r})})}}class N extends P{static lc_name(){return"RunnableRetry"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"maxAttemptNumber",{enumerable:!0,configurable:!0,writable:!0,value:3}),Object.defineProperty(this,"onFailedAttempt",{enumerable:!0,configurable:!0,writable:!0,value:()=>{}}),this.maxAttemptNumber=e.maxAttemptNumber??this.maxAttemptNumber,this.onFailedAttempt=e.onFailedAttempt??this.onFailedAttempt}_patchConfigForRetry(e,t,r){let a=e>1?`retry:attempt:${e}`:void 0;return(0,y.tn)(t,{callbacks:r?.getChild(a)})}async _invoke(e,t,r){return i(a=>super.invoke(e,this._patchConfigForRetry(a,t,r)),{onFailedAttempt:t=>this.onFailedAttempt(t,e),retries:Math.max(this.maxAttemptNumber-1,0),randomize:!0})}async invoke(e,t){return this._callWithConfig(this._invoke.bind(this),e,t)}async _batch(e,t,r,a){let n={};try{await i(async i=>{let s,o=e.map((e,t)=>t).filter(e=>void 0===n[e.toString()]||n[e.toString()]instanceof Error),l=o.map(t=>e[t]),u=o.map(e=>this._patchConfigForRetry(i,t?.[e],r?.[e])),c=await super.batch(l,u,{...a,returnExceptions:!0});for(let e=0;e<c.length;e+=1){let t=c[e],r=o[e];t instanceof Error&&void 0===s&&((s=t).input=l[e]),n[r.toString()]=t}if(s)throw s;return c},{onFailedAttempt:e=>this.onFailedAttempt(e,e.input),retries:Math.max(this.maxAttemptNumber-1,0),randomize:!0})}catch(e){if(a?.returnExceptions!==!0)throw e}return Object.keys(n).sort((e,t)=>parseInt(e,10)-parseInt(t,10)).map(e=>n[parseInt(e,10)])}async batch(e,t,r){return this._batchWithConfig(this._batch.bind(this),e,t,r)}}class C extends j{static lc_name(){return"RunnableSequence"}constructor(e){super(e),Object.defineProperty(this,"first",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"middle",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"last",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"omitSequenceTags",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),this.first=e.first,this.middle=e.middle??this.middle,this.last=e.last,this.name=e.name,this.omitSequenceTags=e.omitSequenceTags??this.omitSequenceTags}get steps(){return[this.first,...this.middle,this.last]}async invoke(e,t){let r,a=(0,y.ZI)(t),i=await (0,y.kJ)(a),n=await i?.handleChainStart(this.toJSON(),x(e,"input"),a.runId,void 0,void 0,void 0,a?.runName);delete a.runId;let s=e;try{let e=[this.first,...this.middle];for(let r=0;r<e.length;r+=1){let i=e[r].invoke(s,(0,y.tn)(a,{callbacks:n?.getChild(this.omitSequenceTags?void 0:`seq:step:${r+1}`)}));s=await (0,g.o)(i,t?.signal)}if(t?.signal?.aborted)throw Error("Aborted");r=await this.last.invoke(s,(0,y.tn)(a,{callbacks:n?.getChild(this.omitSequenceTags?void 0:`seq:step:${this.steps.length}`)}))}catch(e){throw await n?.handleChainError(e),e}return await n?.handleChainEnd(x(r,"output")),r}async batch(e,t,r){let a=this._getOptionsList(t??{},e.length),i=await Promise.all(a.map(y.kJ)),n=await Promise.all(i.map(async(t,r)=>{let i=await t?.handleChainStart(this.toJSON(),x(e[r],"input"),a[r].runId,void 0,void 0,void 0,a[r].runName);return delete a[r].runId,i})),s=e;try{for(let e=0;e<this.steps.length;e+=1){let t=this.steps[e].batch(s,n.map((t,r)=>{let i=t?.getChild(this.omitSequenceTags?void 0:`seq:step:${e+1}`);return(0,y.tn)(a[r],{callbacks:i})}),r);s=await (0,g.o)(t,a[0]?.signal)}}catch(e){throw await Promise.all(n.map(t=>t?.handleChainError(e))),e}return await Promise.all(n.map(e=>e?.handleChainEnd(x(s,"output")))),s}async *_streamIterator(e,t){let r,a=await (0,y.kJ)(t),{runId:i,...n}=t??{},s=await a?.handleChainStart(this.toJSON(),x(e,"input"),i,void 0,void 0,void 0,n?.runName),o=[this.first,...this.middle,this.last],l=!0;async function*c(){yield e}try{let e=o[0].transform(c(),(0,y.tn)(n,{callbacks:s?.getChild(this.omitSequenceTags?void 0:"seq:step:1")}));for(let t=1;t<o.length;t+=1){let r=o[t];e=await r.transform(e,(0,y.tn)(n,{callbacks:s?.getChild(this.omitSequenceTags?void 0:`seq:step:${t+1}`)}))}for await(let a of e)if(t?.signal?.throwIfAborted(),yield a,l)if(void 0===r)r=a;else try{r=(0,u.concat)(r,a)}catch(e){r=void 0,l=!1}}catch(e){throw await s?.handleChainError(e),e}await s?.handleChainEnd(x(r,"output"))}getGraph(e){let t=new E.T,r=null;return this.steps.forEach((a,i)=>{let n=a.getGraph(e);0!==i&&n.trimFirstNode(),i!==this.steps.length-1&&n.trimLastNode(),t.extend(n);let s=n.firstNode();if(!s)throw Error(`Runnable ${a} has no first node`);r&&t.addEdge(r,s),r=n.lastNode()}),t}pipe(e){return new C(C.isRunnableSequence(e)?{first:this.first,middle:this.middle.concat([this.last,e.first,...e.middle]),last:e.last,name:this.name??e.name}:{first:this.first,middle:[...this.middle,this.last],last:F(e),name:this.name})}static isRunnableSequence(e){return Array.isArray(e.middle)&&j.isRunnable(e)}static from([e,...t],r){let a={};return"string"==typeof r?a.name=r:void 0!==r&&(a=r),new C({...a,first:F(e),middle:t.slice(0,-1).map(F),last:F(t[t.length-1])})}}class L extends j{static lc_name(){return"RunnableMap"}getStepsKeys(){return Object.keys(this.steps)}constructor(e){for(let[t,r]of(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"steps",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.steps={},Object.entries(e.steps)))this.steps[t]=F(r)}static from(e){return new L({steps:e})}async invoke(e,t){let r=(0,y.ZI)(t),a=await (0,y.kJ)(r),i=await a?.handleChainStart(this.toJSON(),{input:e},r.runId,void 0,void 0,void 0,r?.runName);delete r.runId;let n={};try{let a=Object.entries(this.steps).map(async([t,a])=>{n[t]=await a.invoke(e,(0,y.tn)(r,{callbacks:i?.getChild(`map:key:${t}`)}))});await (0,g.o)(Promise.all(a),t?.signal)}catch(e){throw await i?.handleChainError(e),e}return await i?.handleChainEnd(n),n}async *_transform(e,t,r){let a={...this.steps},i=(0,u.atee)(e,Object.keys(a).length),n=new Map(Object.entries(a).map(([e,a],n)=>{let s=a.transform(i[n],(0,y.tn)(r,{callbacks:t?.getChild(`map:key:${e}`)}));return[e,s.next().then(t=>({key:e,gen:s,result:t}))]}));for(;n.size;){let e=Promise.race(n.values()),{key:t,result:a,gen:i}=await (0,g.o)(e,r?.signal);n.delete(t),a.done||(yield{[t]:a.value},n.set(t,i.next().then(e=>({key:t,gen:i,result:e}))))}}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let a=(0,y.ZI)(t),i=new u.AsyncGeneratorWithSetup({generator:this.transform(r(),a),config:a});return await i.setup,u.IterableReadableStream.fromAsyncGenerator(i)}}class M extends j{constructor(e){if(super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),!(0,s.GZ)(e.func))throw Error("RunnableTraceable requires a function that is wrapped in traceable higher-order function");this.func=e.func}async invoke(e,t){let[r]=this._getOptionsList(t??{},1),a=await (0,y.kJ)(r),i=this.func((0,y.tn)(r,{callbacks:a}),e);return(0,g.o)(i,r?.signal)}async *_streamIterator(e,t){let[r]=this._getOptionsList(t??{},1),a=await this.invoke(e,t);if(I(a)){for await(let e of a)r?.signal?.throwIfAborted(),yield e;return}if($(a)){for(;;){r?.signal?.throwIfAborted();let e=a.next();if(e.done)break;yield e.value}return}yield a}static from(e){return new M({func:e})}}class z extends j{static lc_name(){return"RunnableLambda"}constructor(e){if((0,s.GZ)(e.func))return M.from(e.func);super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0});var t=e.func;if((0,s.GZ)(t))throw Error("RunnableLambda requires a function that is not wrapped in traceable higher-order function. This shouldn't happen.");this.func=e.func}static from(e){return new z({func:e})}async _invoke(e,t,r){return new Promise((a,i)=>{let n=(0,y.tn)(t,{callbacks:r?.getChild(),recursionLimit:(t?.recursionLimit??y.p_)-1});w.Nx.runWithConfig((0,y.DY)(n),async()=>{try{let r=await this.func(e,{...n});if(r&&j.isRunnable(r)){if(t?.recursionLimit===0)throw Error("Recursion limit reached.");r=await r.invoke(e,{...n,recursionLimit:(n.recursionLimit??y.p_)-1})}else if(I(r)){let e;for await(let a of k(n,r))if(t?.signal?.throwIfAborted(),void 0===e)e=a;else try{e=(0,u.concat)(e,a)}catch(t){e=a}r=e}else if(O(r)){let e;for(let a of S(n,r))if(t?.signal?.throwIfAborted(),void 0===e)e=a;else try{e=(0,u.concat)(e,a)}catch(t){e=a}r=e}a(r)}catch(e){i(e)}})})}async invoke(e,t){return this._callWithConfig(this._invoke.bind(this),e,t)}async *_transform(e,t,r){let a;for await(let t of e)if(void 0===a)a=t;else try{a=(0,u.concat)(a,t)}catch(e){a=t}let i=(0,y.tn)(r,{callbacks:t?.getChild(),recursionLimit:(r?.recursionLimit??y.p_)-1}),n=await new Promise((e,t)=>{w.Nx.runWithConfig((0,y.DY)(i),async()=>{try{let t=await this.func(a,{...i,config:i});e(t)}catch(e){t(e)}})});if(n&&j.isRunnable(n)){if(r?.recursionLimit===0)throw Error("Recursion limit reached.");for await(let e of(await n.stream(a,i)))yield e}else if(I(n))for await(let e of k(i,n))r?.signal?.throwIfAborted(),yield e;else if(O(n))for(let e of S(i,n))r?.signal?.throwIfAborted(),yield e;else yield n}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let a=(0,y.ZI)(t),i=new u.AsyncGeneratorWithSetup({generator:this.transform(r(),a),config:a});return await i.setup,u.IterableReadableStream.fromAsyncGenerator(i)}}class U extends L{}class D extends j{static lc_name(){return"RunnableWithFallbacks"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"runnable",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"fallbacks",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.runnable=e.runnable,this.fallbacks=e.fallbacks}*runnables(){for(let e of(yield this.runnable,this.fallbacks))yield e}async invoke(e,t){let r=(0,y.ZI)(t),a=await (0,y.kJ)(r),{runId:i,...n}=r,s=await a?.handleChainStart(this.toJSON(),x(e,"input"),i,void 0,void 0,void 0,n?.runName),o=(0,y.tn)(n,{callbacks:s?.getChild()});return await w.Nx.runWithConfig(o,async()=>{let t;for(let a of this.runnables()){r?.signal?.throwIfAborted();try{let t=await a.invoke(e,o);return await s?.handleChainEnd(x(t,"output")),t}catch(e){void 0===t&&(t=e)}}if(void 0===t)throw Error("No error stored at end of fallback.");throw await s?.handleChainError(t),t})}async *_streamIterator(e,t){let r,a,i,n=(0,y.ZI)(t),s=await (0,y.kJ)(n),{runId:o,...l}=n,c=await s?.handleChainStart(this.toJSON(),x(e,"input"),o,void 0,void 0,void 0,l?.runName);for(let t of this.runnables()){n?.signal?.throwIfAborted();let i=(0,y.tn)(l,{callbacks:c?.getChild()});try{let r=await t.stream(e,i);a=k(i,r);break}catch(e){void 0===r&&(r=e)}}if(void 0===a){let e=r??Error("No error stored at end of fallback.");throw await c?.handleChainError(e),e}try{for await(let e of a){yield e;try{i=void 0===i?i:(0,u.concat)(i,e)}catch(e){i=void 0}}}catch(e){throw await c?.handleChainError(e),e}await c?.handleChainEnd(x(i,"output"))}async batch(e,t,r){let a;if(r?.returnExceptions)throw Error("Not implemented.");let i=this._getOptionsList(t??{},e.length),n=await Promise.all(i.map(e=>(0,y.kJ)(e))),s=await Promise.all(n.map(async(t,r)=>{let a=await t?.handleChainStart(this.toJSON(),x(e[r],"input"),i[r].runId,void 0,void 0,void 0,i[r].runName);return delete i[r].runId,a}));for(let t of this.runnables()){i[0].signal?.throwIfAborted();try{let a=await t.batch(e,s.map((e,t)=>(0,y.tn)(i[t],{callbacks:e?.getChild()})),r);return await Promise.all(s.map((e,t)=>e?.handleChainEnd(x(a[t],"output")))),a}catch(e){void 0===a&&(a=e)}}if(!a)throw Error("No error stored at end of fallbacks.");throw await Promise.all(s.map(e=>e?.handleChainError(a))),a}}function F(e){if("function"==typeof e)return new z({func:e});if(j.isRunnable(e))return e;if(Array.isArray(e)||"object"!=typeof e)throw Error(`Expected a Runnable, function or object.
Instead got an unsupported type.`);{let t={};for(let[r,a]of Object.entries(e))t[r]=F(a);return new L({steps:t})}}class B extends j{static lc_name(){return"RunnableAssign"}constructor(e){e instanceof L&&(e={mapper:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"mapper",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.mapper=e.mapper}async invoke(e,t){let r=await this.mapper.invoke(e,t);return{...e,...r}}async *_transform(e,t,r){let a=this.mapper.getStepsKeys(),[i,n]=(0,u.atee)(e),s=this.mapper.transform(n,(0,y.tn)(r,{callbacks:t?.getChild()})),o=s.next();for await(let e of i){if("object"!=typeof e||Array.isArray(e))throw Error(`RunnableAssign can only be used with objects as input, got ${typeof e}`);let t=Object.fromEntries(Object.entries(e).filter(([e])=>!a.includes(e)));Object.keys(t).length>0&&(yield t)}for await(let e of(yield(await o).value,s))yield e}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let a=(0,y.ZI)(t),i=new u.AsyncGeneratorWithSetup({generator:this.transform(r(),a),config:a});return await i.setup,u.IterableReadableStream.fromAsyncGenerator(i)}}class H extends j{static lc_name(){return"RunnablePick"}constructor(e){("string"==typeof e||Array.isArray(e))&&(e={keys:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"keys",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.keys=e.keys}async _pick(e){if("string"==typeof this.keys)return e[this.keys];{let t=this.keys.map(t=>[t,e[t]]).filter(e=>void 0!==e[1]);return 0===t.length?void 0:Object.fromEntries(t)}}async invoke(e,t){return this._callWithConfig(this._pick.bind(this),e,t)}async *_transform(e){for await(let t of e){let e=await this._pick(t);void 0!==e&&(yield e)}}transform(e,t){return this._transformStreamWithConfig(e,this._transform.bind(this),t)}async stream(e,t){async function*r(){yield e}let a=(0,y.ZI)(t),i=new u.AsyncGeneratorWithSetup({generator:this.transform(r(),a),config:a});return await i.setup,u.IterableReadableStream.fromAsyncGenerator(i)}}class G extends P{constructor(e){super({bound:C.from([z.from(async e=>{let t;if((0,A.Ky)(e))try{t=await (0,T.hZ)(this.schema,e.args)}catch(t){throw new A.qe("Received tool input did not match expected schema",JSON.stringify(e.args))}else t=e;return t}).withConfig({runName:`${e.name}:parse_input`}),e.bound]).withConfig({runName:e.name}),config:e.config??{}}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"description",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.description=e.description,this.schema=e.schema}static lc_name(){return"RunnableToolLike"}}},90726:(e,t,r)=>{"use strict";let a=r(64487);e.exports=(e,t,r,i,n)=>{"string"==typeof r&&(n=i,i=r,r=void 0);try{return new a(e instanceof a?e.version:e,r).inc(t,i,n).version}catch(e){return null}}},91405:(e,t,r)=>{"use strict";r.d(t,{X6:()=>_,UD:()=>S});var a={};r.r(a),r.d(a,{JsonPatchError:()=>p,_areEquals:()=>O,applyOperation:()=>b,applyPatch:()=>_,applyReducer:()=>v,deepClone:()=>f,getValueByPointer:()=>y,validate:()=>E,validator:()=>w});let i=Object.prototype.hasOwnProperty;function n(e,t){return i.call(e,t)}function s(e){if(Array.isArray(e)){let t=Array(e.length);for(let e=0;e<t.length;e++)t[e]=""+e;return t}if(Object.keys)return Object.keys(e);let t=[];for(let r in e)n(e,r)&&t.push(r);return t}function o(e){switch(typeof e){case"object":return JSON.parse(JSON.stringify(e));case"undefined":return null;default:return e}}function l(e){let t,r=0,a=e.length;for(;r<a;){if((t=e.charCodeAt(r))>=48&&t<=57){r++;continue}return!1}return!0}function u(e){return -1===e.indexOf("/")&&-1===e.indexOf("~")?e:e.replace(/~/g,"~0").replace(/\//g,"~1")}function c(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function d(e,t){let r=[e];for(let e in t){let a="object"==typeof t[e]?JSON.stringify(t[e],null,2):t[e];void 0!==a&&r.push(`${e}: ${a}`)}return r.join("\n")}class h extends Error{constructor(e,t,r,a,i){super(d(e,{name:t,index:r,operation:a,tree:i})),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"index",{enumerable:!0,configurable:!0,writable:!0,value:r}),Object.defineProperty(this,"operation",{enumerable:!0,configurable:!0,writable:!0,value:a}),Object.defineProperty(this,"tree",{enumerable:!0,configurable:!0,writable:!0,value:i}),Object.setPrototypeOf(this,new.target.prototype),this.message=d(e,{name:t,index:r,operation:a,tree:i})}}let p=h,f=o,m={add:function(e,t,r){return e[t]=this.value,{newDocument:r}},remove:function(e,t,r){var a=e[t];return delete e[t],{newDocument:r,removed:a}},replace:function(e,t,r){var a=e[t];return e[t]=this.value,{newDocument:r,removed:a}},move:function(e,t,r){let a=y(r,this.path);a&&(a=o(a));let i=b(r,{op:"remove",path:this.from}).removed;return b(r,{op:"add",path:this.path,value:i}),{newDocument:r,removed:a}},copy:function(e,t,r){let a=y(r,this.from);return b(r,{op:"add",path:this.path,value:o(a)}),{newDocument:r}},test:function(e,t,r){return{newDocument:r,test:O(e[t],this.value)}},_get:function(e,t,r){return this.value=e[t],{newDocument:r}}};var g={add:function(e,t,r){return l(t)?e.splice(t,0,this.value):e[t]=this.value,{newDocument:r,index:t}},remove:function(e,t,r){return{newDocument:r,removed:e.splice(t,1)[0]}},replace:function(e,t,r){var a=e[t];return e[t]=this.value,{newDocument:r,removed:a}},move:m.move,copy:m.copy,test:m.test,_get:m._get};function y(e,t){if(""==t)return e;var r={op:"_get",path:t};return b(e,r),r.value}function b(e,t,r=!1,a=!0,i=!0,n=0){if(r&&("function"==typeof r?r(t,0,e,t.path):w(t,0)),""===t.path){let a={newDocument:e};if("add"===t.op)return a.newDocument=t.value,a;if("replace"===t.op)return a.newDocument=t.value,a.removed=e,a;if("move"===t.op||"copy"===t.op)return a.newDocument=y(e,t.from),"move"===t.op&&(a.removed=e),a;else if("test"===t.op){if(a.test=O(e,t.value),!1===a.test)throw new p("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return a.newDocument=e,a}else if("remove"===t.op)return a.removed=e,a.newDocument=null,a;else if("_get"===t.op)return t.value=e,a;else if(!r)return a;else throw new p("Operation `op` property is not one of operations defined in RFC-6902","OPERATION_OP_INVALID",n,t,e)}{let s,u,d;a||(e=o(e));let h=(t.path||"").split("/"),f=e,y=1,b=h.length;for(u="function"==typeof r?r:w;;){if((s=h[y])&&-1!=s.indexOf("~")&&(s=c(s)),i&&("__proto__"==s||"prototype"==s&&y>0&&"constructor"==h[y-1]))throw TypeError("JSON-Patch: modifying `__proto__` or `constructor/prototype` prop is banned for security reasons, if this was on purpose, please set `banPrototypeModifications` flag false and pass it to this function. More info in fast-json-patch README");if(r&&void 0===d&&(void 0===f[s]?d=h.slice(0,y).join("/"):y==b-1&&(d=t.path),void 0!==d&&u(t,0,e,d)),y++,Array.isArray(f)){if("-"===s)s=f.length;else if(r&&!l(s))throw new p("Expected an unsigned base-10 integer value, making the new referenced value the array element with the zero-based index","OPERATION_PATH_ILLEGAL_ARRAY_INDEX",n,t,e);else l(s)&&(s=~~s);if(y>=b){if(r&&"add"===t.op&&s>f.length)throw new p("The specified index MUST NOT be greater than the number of elements in the array","OPERATION_VALUE_OUT_OF_BOUNDS",n,t,e);let a=g[t.op].call(t,f,s,e);if(!1===a.test)throw new p("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return a}}else if(y>=b){let r=m[t.op].call(t,f,s,e);if(!1===r.test)throw new p("Test operation failed","TEST_OPERATION_FAILED",n,t,e);return r}if(f=f[s],r&&y<b&&(!f||"object"!=typeof f))throw new p("Cannot perform operation at the desired path","OPERATION_PATH_UNRESOLVABLE",n,t,e)}}}function _(e,t,r,a=!0,i=!0){if(r&&!Array.isArray(t))throw new p("Patch sequence must be an array","SEQUENCE_NOT_AN_ARRAY");a||(e=o(e));let n=Array(t.length);for(let a=0,s=t.length;a<s;a++)n[a]=b(e,t[a],r,!0,i,a),e=n[a].newDocument;return n.newDocument=e,n}function v(e,t,r){let a=b(e,t);if(!1===a.test)throw new p("Test operation failed","TEST_OPERATION_FAILED",r,t,e);return a.newDocument}function w(e,t,r,a){if("object"!=typeof e||null===e||Array.isArray(e))throw new p("Operation is not an object","OPERATION_NOT_AN_OBJECT",t,e,r);if(m[e.op]){if("string"!=typeof e.path)throw new p("Operation `path` property is not a string","OPERATION_PATH_INVALID",t,e,r);else if(0!==e.path.indexOf("/")&&e.path.length>0)throw new p('Operation `path` property must start with "/"',"OPERATION_PATH_INVALID",t,e,r);else if(("move"===e.op||"copy"===e.op)&&"string"!=typeof e.from)throw new p("Operation `from` property is not present (applicable in `move` and `copy` operations)","OPERATION_FROM_REQUIRED",t,e,r);else if(("add"===e.op||"replace"===e.op||"test"===e.op)&&void 0===e.value)throw new p("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_REQUIRED",t,e,r);else if(("add"===e.op||"replace"===e.op||"test"===e.op)&&function e(t){if(void 0===t)return!0;if(t){if(Array.isArray(t)){for(let r=0,a=t.length;r<a;r++)if(e(t[r]))return!0}else if("object"==typeof t){let a=s(t),i=a.length;for(var r=0;r<i;r++)if(e(t[a[r]]))return!0}}return!1}(e.value))throw new p("Operation `value` property is not present (applicable in `add`, `replace` and `test` operations)","OPERATION_VALUE_CANNOT_CONTAIN_UNDEFINED",t,e,r);else if(r){if("add"==e.op){var i=e.path.split("/").length,n=a.split("/").length;if(i!==n+1&&i!==n)throw new p("Cannot perform an `add` operation at the desired path","OPERATION_PATH_CANNOT_ADD",t,e,r)}else if("replace"===e.op||"remove"===e.op||"_get"===e.op){if(e.path!==a)throw new p("Cannot perform the operation at a path that does not exist","OPERATION_PATH_UNRESOLVABLE",t,e,r)}else if("move"===e.op||"copy"===e.op){var o=E([{op:"_get",path:e.from,value:void 0}],r);if(o&&"OPERATION_PATH_UNRESOLVABLE"===o.name)throw new p("Cannot perform the operation from a path that does not exist","OPERATION_FROM_UNRESOLVABLE",t,e,r)}}}else throw new p("Operation `op` property is not one of operations defined in RFC-6902","OPERATION_OP_INVALID",t,e,r)}function E(e,t,r){try{if(!Array.isArray(e))throw new p("Patch sequence must be an array","SEQUENCE_NOT_AN_ARRAY");if(t)_(o(t),o(e),r||!0);else{r=r||w;for(var a=0;a<e.length;a++)r(e[a],a,t,void 0)}}catch(e){if(e instanceof p)return e;throw e}}function O(e,t){if(e===t)return!0;if(e&&t&&"object"==typeof e&&"object"==typeof t){var r,a,i,n=Array.isArray(e),s=Array.isArray(t);if(n&&s){if((a=e.length)!=t.length)return!1;for(r=a;0!=r--;)if(!O(e[r],t[r]))return!1;return!0}if(n!=s)return!1;var o=Object.keys(e);if((a=o.length)!==Object.keys(t).length)return!1;for(r=a;0!=r--;)if(!t.hasOwnProperty(o[r]))return!1;for(r=a;0!=r--;)if(!O(e[i=o[r]],t[i]))return!1;return!0}return e!=e&&t!=t}var $=new WeakMap;function I(e,t,r,a,i){if(t!==e){"function"==typeof t.toJSON&&(t=t.toJSON());for(var l=s(t),c=s(e),d=!1,h=c.length-1;h>=0;h--){var p=c[h],f=e[p];if(n(t,p)&&(void 0!==t[p]||void 0===f||!1!==Array.isArray(t))){var m=t[p];"object"==typeof f&&null!=f&&"object"==typeof m&&null!=m&&Array.isArray(f)===Array.isArray(m)?I(f,m,r,a+"/"+u(p),i):f!==m&&(i&&r.push({op:"test",path:a+"/"+u(p),value:o(f)}),r.push({op:"replace",path:a+"/"+u(p),value:o(m)}))}else Array.isArray(e)===Array.isArray(t)?(i&&r.push({op:"test",path:a+"/"+u(p),value:o(f)}),r.push({op:"remove",path:a+"/"+u(p)}),d=!0):(i&&r.push({op:"test",path:a,value:e}),r.push({op:"replace",path:a,value:t}))}if(d||l.length!=c.length)for(var h=0;h<l.length;h++){var p=l[h];n(e,p)||void 0===t[p]||r.push({op:"add",path:a+"/"+u(p),value:o(t[p])})}}}function S(e,t,r=!1){var a=[];return I(e,t,a,"",r),a}({...a,JsonPatchError:h,deepClone:o,escapePathComponent:u,unescapePathComponent:c})},91962:(e,t,r)=>{"use strict";r.d(t,{Kj:()=>a.Kj,gk:()=>a.gk,q7:()=>a.q7});var a=r(30663)},93419:(e,t,r)=>{"use strict";let a=r(58361);e.exports=(e,t)=>{let r=a(e,null,!0),i=a(t,null,!0),n=r.compare(i);if(0===n)return null;let s=n>0,o=s?r:i,l=s?i:r,u=!!o.prerelease.length;if(l.prerelease.length&&!u){if(!l.patch&&!l.minor)return"major";if(0===l.compareMain(o))return l.minor&&!l.patch?"minor":"patch"}let c=u?"pre":"";return r.major!==i.major?c+"major":r.minor!==i.minor?c+"minor":r.patch!==i.patch?c+"patch":"prerelease"}},97386:(e,t,r)=>{"use strict";r.d(t,{$o:()=>n,O3:()=>s,d4:()=>o});var a=r(57543),i=r(51862);function n(e,t){return t?.[e]||a(e)}function s(e,t){return t?.[e]||i(e)}function o(e,t,r){let a={};for(let i in e)Object.hasOwn(e,i)&&(a[t(i,r)]=e[i]);return a}},98300:e=>{"use strict";let t=Object.freeze({loose:!0}),r=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:r},98431:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(89386),i=r(89136),n=r(77598),s=r.n(n);let o=function(e,t,r){function n(e,t,n,s){var o;if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));let t=[];for(let r=0;r<e.length;++r)t.push(e.charCodeAt(r));return t}(e)),"string"==typeof t&&(t=(0,i.A)(t)),(null==(o=t)?void 0:o.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let l=new Uint8Array(16+e.length);if(l.set(t),l.set(e,t.length),(l=r(l))[6]=15&l[6]|80,l[8]=63&l[8]|128,n){s=s||0;for(let e=0;e<16;++e)n[s+e]=l[e];return n}return(0,a.k)(l)}try{n.name="v5"}catch(e){}return n.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",n.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",n}(0,0,function(e){return Array.isArray(e)?e=Buffer.from(e):"string"==typeof e&&(e=Buffer.from(e,"utf8")),s().createHash("sha1").update(e).digest()})},98488:(e,t,r)=>{"use strict";r.d(t,{UF:()=>o,X4:()=>s,tn:()=>i,uU:()=>n});var a=r(72892);class i extends a.XQ{static lc_name(){return"SystemMessage"}_getType(){return"system"}constructor(e,t){super(e,t)}}class n extends a.gj{static lc_name(){return"SystemMessageChunk"}_getType(){return"system"}constructor(e,t){super(e,t)}concat(e){return new n({content:(0,a._I)(this.content,e.content),additional_kwargs:(0,a.ns)(this.additional_kwargs,e.additional_kwargs),response_metadata:(0,a.ns)(this.response_metadata,e.response_metadata),id:this.id??e.id})}}function s(e){return"system"===e._getType()}function o(e){return"system"===e._getType()}}};