/**
 * @license lucide-react v0.516.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M5 3h14", key: "7usisc" }],
  ["path", { d: "m18 13-6-6-6 6", key: "1kf1n9" }],
  ["path", { d: "M12 7v14", key: "1akyts" }]
];
const ArrowUpToLine = createLucideIcon("arrow-up-to-line", __iconNode);

export { __iconNode, ArrowUpToLine as default };
//# sourceMappingURL=arrow-up-to-line.js.map
