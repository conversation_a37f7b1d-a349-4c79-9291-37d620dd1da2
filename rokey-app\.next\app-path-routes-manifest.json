{"/_not-found/page": "/_not-found", "/api/analytics/summary/route": "/api/analytics/summary", "/api/chat/conversations/route": "/api/chat/conversations", "/api/cache/invalidate/route": "/api/cache/invalidate", "/api/chat/messages/route": "/api/chat/messages", "/api/chat/messages/delete-after-timestamp/route": "/api/chat/messages/delete-after-timestamp", "/api/activity/route": "/api/activity", "/api/custom-configs/[configId]/default-chat-key/route": "/api/custom-configs/[configId]/default-chat-key", "/api/cleanup/pending-users/route": "/api/cleanup/pending-users", "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]/route": "/api/custom-configs/[configId]/default-key-handler/[apiKeyId]", "/api/chat/messages/update-by-timestamp/route": "/api/chat/messages/update-by-timestamp", "/api/custom-configs/[configId]/routing/route": "/api/custom-configs/[configId]/routing", "/api/debug/checkout/route": "/api/debug/checkout", "/api/custom-configs/route": "/api/custom-configs", "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments/route": "/api/custom-configs/[configId]/keys/[apiKeyId]/complexity-assignments", "/api/debug/clear-cache/route": "/api/debug/clear-cache", "/api/documents/list/route": "/api/documents/list", "/api/custom-configs/[configId]/route": "/api/custom-configs/[configId]", "/api/documents/search/route": "/api/documents/search", "/api/documents/upload/route": "/api/documents/upload", "/api/documents/[documentId]/route": "/api/documents/[documentId]", "/api/keys/[apiKeyId]/roles/[roleName]/route": "/api/keys/[apiKeyId]/roles/[roleName]", "/api/keys/[apiKeyId]/roles/route": "/api/keys/[apiKeyId]/roles", "/api/debug/supabase-test/route": "/api/debug/supabase-test", "/api/keys/route": "/api/keys", "/api/logs/route": "/api/logs", "/api/keys/[apiKeyId]/route": "/api/keys/[apiKeyId]", "/api/orchestration/stream/[executionId]/route": "/api/orchestration/stream/[executionId]", "/api/orchestration/process-step/route": "/api/orchestration/process-step", "/api/orchestration/status/[executionId]/route": "/api/orchestration/status/[executionId]", "/api/orchestration/start/route": "/api/orchestration/start", "/api/orchestration/synthesis-fallback/[executionId]/route": "/api/orchestration/synthesis-fallback/[executionId]", "/api/orchestration/synthesis-stream-direct/[executionId]/route": "/api/orchestration/synthesis-stream-direct/[executionId]", "/api/providers/list-models/route": "/api/providers/list-models", "/api/orchestration/synthesis-stream/[executionId]/route": "/api/orchestration/synthesis-stream/[executionId]", "/api/stripe/customer-portal/route": "/api/stripe/customer-portal", "/api/stripe/subscription-status/route": "/api/stripe/subscription-status", "/api/stripe/create-checkout-session/route": "/api/stripe/create-checkout-session", "/api/system-status/route": "/api/system-status", "/api/stripe/webhooks/route": "/api/stripe/webhooks", "/api/playground/route": "/api/playground", "/api/pricing/tiers/route": "/api/pricing/tiers", "/api/training/jobs/route": "/api/training/jobs", "/api/user/custom-roles/[customRoleId]/route": "/api/user/custom-roles/[customRoleId]", "/api/training/jobs/upsert/route": "/api/training/jobs/upsert", "/api/user/custom-roles/route": "/api/user/custom-roles", "/api/v1/chat/completions/route": "/api/v1/chat/completions", "/auth/callback/route": "/auth/callback", "/favicon.ico/route": "/favicon.ico", "/analytics/page": "/analytics", "/about/page": "/about", "/add-keys/page": "/add-keys", "/auth/signin/page": "/auth/signin", "/dashboard/page": "/dashboard", "/auth/signup/page": "/auth/signup", "/logs/page": "/logs", "/my-models/[configId]/page": "/my-models/[configId]", "/features/page": "/features", "/my-models/page": "/my-models", "/playground/page": "/playground", "/page": "/", "/pricing/page": "/pricing", "/routing-setup/page": "/routing-setup", "/routing-setup/[configId]/page": "/routing-setup/[configId]", "/training/page": "/training", "/auth/verify-email/page": "/auth/verify-email", "/debug-session/page": "/debug-session", "/checkout/page": "/checkout"}