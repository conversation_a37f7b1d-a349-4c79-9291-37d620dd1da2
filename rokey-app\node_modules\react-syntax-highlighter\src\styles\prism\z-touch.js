export default {
    "code[class*=\"language-\"]": {
        "color": "#22da17",
        "fontFamily": "monospace",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "wordWrap": "normal",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "lineHeight": "25px",
        "fontSize": "18px",
        "margin": "5px 0"
    },
    "pre[class*=\"language-\"]": {
        "color": "white",
        "fontFamily": "monospace",
        "textAlign": "left",
        "whiteSpace": "pre",
        "wordSpacing": "normal",
        "wordBreak": "normal",
        "wordWrap": "normal",
        "MozTabSize": "4",
        "OTabSize": "4",
        "tabSize": "4",
        "WebkitHyphens": "none",
        "MozHyphens": "none",
        "msHyphens": "none",
        "hyphens": "none",
        "lineHeight": "25px",
        "fontSize": "18px",
        "margin": "0.5em 0",
        "background": "#0a143c",
        "padding": "1em",
        "overflow": "auto"
    },
    "pre[class*=\"language-\"] *": {
        "fontFamily": "monospace"
    },
    ":not(pre) > code[class*=\"language-\"]": {
        "color": "white",
        "background": "#0a143c",
        "padding": "0.1em",
        "borderRadius": "0.3em",
        "whiteSpace": "normal"
    },
    "pre[class*=\"language-\"]::-moz-selection": {
        "textShadow": "none",
        "background": "rgba(29, 59, 83, 0.99)"
    },
    "pre[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "rgba(29, 59, 83, 0.99)"
    },
    "code[class*=\"language-\"]::-moz-selection": {
        "textShadow": "none",
        "background": "rgba(29, 59, 83, 0.99)"
    },
    "code[class*=\"language-\"] ::-moz-selection": {
        "textShadow": "none",
        "background": "rgba(29, 59, 83, 0.99)"
    },
    "pre[class*=\"language-\"]::selection": {
        "textShadow": "none",
        "background": "rgba(29, 59, 83, 0.99)"
    },
    "pre[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "rgba(29, 59, 83, 0.99)"
    },
    "code[class*=\"language-\"]::selection": {
        "textShadow": "none",
        "background": "rgba(29, 59, 83, 0.99)"
    },
    "code[class*=\"language-\"] ::selection": {
        "textShadow": "none",
        "background": "rgba(29, 59, 83, 0.99)"
    },
    "comment": {
        "color": "rgb(99, 119, 119)",
        "fontStyle": "italic"
    },
    "prolog": {
        "color": "rgb(99, 119, 119)",
        "fontStyle": "italic"
    },
    "cdata": {
        "color": "rgb(99, 119, 119)",
        "fontStyle": "italic"
    },
    "punctuation": {
        "color": "rgb(199, 146, 234)"
    },
    ".namespace": {
        "color": "rgb(178, 204, 214)"
    },
    "deleted": {
        "color": "rgba(239, 83, 80, 0.56)",
        "fontStyle": "italic"
    },
    "symbol": {
        "color": "rgb(128, 203, 196)"
    },
    "property": {
        "color": "rgb(128, 203, 196)"
    },
    "tag": {
        "color": "rgb(127, 219, 202)"
    },
    "operator": {
        "color": "rgb(127, 219, 202)"
    },
    "keyword": {
        "color": "rgb(127, 219, 202)"
    },
    "boolean": {
        "color": "rgb(255, 88, 116)"
    },
    "number": {
        "color": "rgb(247, 140, 108)"
    },
    "constant": {
        "color": "rgb(34 183 199)"
    },
    "function": {
        "color": "rgb(34 183 199)"
    },
    "builtin": {
        "color": "rgb(34 183 199)"
    },
    "char": {
        "color": "rgb(34 183 199)"
    },
    "selector": {
        "color": "rgb(199, 146, 234)",
        "fontStyle": "italic"
    },
    "doctype": {
        "color": "rgb(199, 146, 234)",
        "fontStyle": "italic"
    },
    "attr-name": {
        "color": "rgb(173, 219, 103)",
        "fontStyle": "italic"
    },
    "inserted": {
        "color": "rgb(173, 219, 103)",
        "fontStyle": "italic"
    },
    "string": {
        "color": "rgb(173, 219, 103)"
    },
    "url": {
        "color": "rgb(173, 219, 103)"
    },
    "entity": {
        "color": "rgb(173, 219, 103)"
    },
    ".language-css .token.string": {
        "color": "rgb(173, 219, 103)"
    },
    ".style .token.string": {
        "color": "rgb(173, 219, 103)"
    },
    "class-name": {
        "color": "rgb(255, 203, 139)"
    },
    "atrule": {
        "color": "rgb(255, 203, 139)"
    },
    "attr-value": {
        "color": "rgb(255, 203, 139)"
    },
    "regex": {
        "color": "rgb(214, 222, 235)"
    },
    "important": {
        "color": "rgb(214, 222, 235)",
        "fontWeight": "bold"
    },
    "variable": {
        "color": "rgb(214, 222, 235)"
    },
    "bold": {
        "fontWeight": "bold"
    },
    "italic": {
        "fontStyle": "italic"
    }
}